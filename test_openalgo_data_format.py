#!/usr/bin/env python3
"""
Test script to check OpenAlgo data format and fix timestamp issues
"""

import json
import sys
import os
from engine.core.data.openalgo_client import OpenAlgoClient

def test_openalgo_data_format():
    """Test OpenAlgo data format to understand the timestamp issue"""
    
    # Load the EMA crossover config
    config_path = "config/ema_crossover.json"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print("🧪 Testing OpenAlgo Data Format")
    print("=" * 50)
    
    # Create OpenAlgo client
    try:
        client = OpenAlgoClient(config)
        print(f"✅ OpenAlgo client created for {config['symbol']}")
    except Exception as e:
        print(f"❌ Failed to create OpenAlgo client: {e}")
        return False
    
    # Test 1: Raw API response
    print("\n📋 Test 1: Raw History API Response")
    try:
        # Make direct API call to see raw response
        from datetime import datetime, timedelta
        now = datetime.now()
        start_date = (now - timedelta(hours=2)).strftime("%Y-%m-%d")
        end_date = now.strftime("%Y-%m-%d")
        
        symbol = config['symbol'].split(":")[-1] if ":" in config['symbol'] else config['symbol']
        
        request_data = {
            "apikey": client.openalgo_api_key,
            "symbol": symbol,
            "exchange": config.get('exchange', 'NSE'),
            "interval": config.get("timeframe", "1m"),
            "start_date": start_date,
            "end_date": end_date
        }
        
        print(f"🔄 Making API request with data: {request_data}")
        
        response = client._make_request("POST", "/api/v1/history", data=request_data)
        
        print(f"📤 Raw API Response:")
        print(f"   Status: {response.get('status')}")
        print(f"   Data type: {type(response.get('data'))}")
        
        if response.get('data'):
            data = response['data']
            if isinstance(data, list) and len(data) > 0:
                print(f"   First row: {data[0]}")
                print(f"   Data keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
            elif isinstance(data, dict):
                print(f"   Data keys: {list(data.keys())}")
                print(f"   Sample data: {str(data)[:200]}...")
            else:
                print(f"   Data: {data}")
        else:
            print("   No data in response")
            
    except Exception as e:
        print(f"❌ Raw API test failed: {e}")
        return False
    
    # Test 2: DataFrame conversion
    print("\n📋 Test 2: DataFrame Conversion")
    try:
        import pandas as pd
        
        if response.get('data'):
            data = response['data']
            print(f"🔄 Converting to DataFrame...")
            
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
                print(f"✅ DataFrame created successfully")
                print(f"   Shape: {df.shape}")
                print(f"   Columns: {list(df.columns)}")
                print(f"   Index: {df.index}")
                
                # Check for timestamp column
                if 'timestamp' in df.columns:
                    print(f"   Timestamp column found: {df['timestamp'].head()}")
                else:
                    print(f"   No 'timestamp' column. Available columns: {list(df.columns)}")
                    
                print(f"   First few rows:")
                print(df.head())
                
            else:
                print(f"⚠️ Data is not a list or is empty")
                
    except Exception as e:
        print(f"❌ DataFrame conversion failed: {e}")
        print(f"   Error details: {str(e)}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🚀 ZenStrato OpenAlgo Data Format Test")
    print("Investigating the timestamp error in OHLCV data")
    print()
    
    success = test_openalgo_data_format()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ TEST COMPLETED: Data format analysis complete")
        print("   Check the output above to understand the data structure")
    else:
        print("❌ TEST FAILED: Could not analyze data format")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
