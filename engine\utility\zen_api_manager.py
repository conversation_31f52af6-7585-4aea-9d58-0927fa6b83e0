import uuid
import os
import re
import argparse

# Correctly resolve project root
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
ENV_PATH = os.path.join(BASE_DIR, "TradePilot", ".env")
KEY_NAME = "ZENSTRATO_API_KEY"

def generate_api_key():
    return str(uuid.uuid4())

def get_existing_key():
    if not os.path.exists(ENV_PATH):
        return None
    with open(ENV_PATH, "r") as f:
        for line in f:
            if re.match(rf"^{KEY_NAME}\s*=", line.strip()):
                return line.strip().split("=", 1)[1].strip()
    return None

def write_api_key(new_key):
    updated = False
    lines = []

    if os.path.exists(ENV_PATH):
        with open(ENV_PATH, "r") as f:
            for line in f:
                if re.match(rf"^{KEY_NAME}\s*=", line.strip()):
                    lines.append(f"{KEY_NAME}={new_key}\n")
                    updated = True
                else:
                    lines.append(line)

    if not updated:
        lines.append(f"{KEY_NAME}={new_key}\n")

    with open(ENV_PATH, "w") as f:
        f.writelines(lines)

    print(f"✅ API key written to {ENV_PATH}: {new_key}")
    return new_key

def refresh_api_key():
    return write_api_key(generate_api_key())

# === Entry Point ===
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="ZenStrato API Key Manager")
    parser.add_argument("--new-api", action="store_true", help="Generate and store new API key for ZenStrato")
    parser.add_argument("--check", action="store_true", help="Check existing ZENSTRATO_API_KEY")

    args = parser.parse_args()

    if args.check:
        existing = get_existing_key()
        if existing:
            print(f"🔑 Existing ZENSTRATO_API_KEY: {existing}")
        else:
            print("⚠️ No ZENSTRATO_API_KEY found.")
    elif args.new_api:
        refresh_api_key()
    else:
        print("ℹ️ Use --check to view or --new-api to generate a new key.")
