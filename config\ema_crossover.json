{"strategy_name": "ema_crossover_enhanced", "symbol": "BHEL", "exchange": "NSE", "product": "MIS", "timeframe": "1m", "ema_parameters": {"fast_period": 5, "slow_period": 10, "description": "EMA crossover periods - configurable"}, "capital_management": {"total_capital": 10000, "capital_per_trade": 5000, "capital_allocation_method": "CAPITAL_BASED"}, "quantity_management": {"quantity_calculation_method": "CAPITAL_BASED", "fixed_quantity": null, "min_quantity": 1, "max_quantity": 1000}, "websocket_config": {"enable_websocket": false, "websocket_url": "ws://127.0.0.1:5000/ws", "real_time_data": true, "ltp_streaming": true, "description": "WebSocket configuration for real-time data"}, "broker_config": {"openalgo_api_key": "406a16fcd1e6c652c491fcec23fac6936be503b93cf0aa6cd49029f259250203", "openalgo_host": "http://127.0.0.1:5000", "openalgo_ws_url": "ws://127.0.0.1:5000/ws", "use_openalgo": true, "connection_timeout": 30}, "order_management": {"order_type": "MARKET", "price_type": "MARKET", "validity": "DAY", "order_tag": "EMA_Crossover", "order_timeout": 30}, "risk_management": {"sl_multiplier": 2, "tp_multiplier": 4, "max_position_size": 0.5, "daily_loss_limit": 1000}, "optimization": {"enable_caching": true, "cache_size": 300, "memory_cleanup_interval": 600, "max_api_calls_per_minute": 60}, "legacy_params": {"capital": 10000, "fast_period": 5, "slow_period": 10, "websocket": false, "parallel_orders": false, "max_concurrent_trades": 1}}