import requests

def alert(text):
    print(text)  # Console fallback logging

def send_alert(config, strategy_name, title, payload):
    if not config.get("telegram_enabled", False):
        return

    msg = f"*🔔 {title}*\n🧠 Strategy: `{strategy_name}`\n"
    for k, v in payload.items():
        msg += f"• *{k}*: `{v}`\n"

    try:
        requests.post(config["telegram_url"], json={"message": msg})
    except Exception as e:
        print("⚠️ Telegram alert failed:", e)
