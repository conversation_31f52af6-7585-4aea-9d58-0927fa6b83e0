import pandas as pd
from engine.core.strategy.istrategy import IStrategy

class Strategy(IStrategy):
    def __init__(self, config):
        super().__init__(config)
        self.fast_period = config.get("fast_period", 5)
        self.slow_period = config.get("slow_period", 20)

    def populate_indicators(self, df: pd.DataFrame):
        df["EMA_fast"] = df["close"].ewm(span=self.fast_period).mean()
        df["EMA_slow"] = df["close"].ewm(span=self.slow_period).mean()
        return df

    def entry_signal(self, df: pd.DataFrame):
        if len(df) < 2:
            return False

        prev, curr = df.iloc[-2], df.iloc[-1]
        return prev["EMA_fast"] < prev["EMA_slow"] and curr["EMA_fast"] > curr["EMA_slow"]

    def exit_signal(self, df: pd.DataFrame, current_price: float, entry_price: float = None, position_type: str = "BUY"):
        """
        Enhanced exit signal method compatible with enhanced engine
        """
        if len(df) < 2:
            return False

        prev, curr = df.iloc[-2], df.iloc[-1]

        # Basic crossover exit logic
        crossover_exit = prev["EMA_fast"] > prev["EMA_slow"] and curr["EMA_fast"] < curr["EMA_slow"]

        # Optional: Add stop-loss/take-profit logic if entry_price is provided
        if entry_price and current_price:
            # Simple 2% stop loss and 4% take profit
            if position_type == "BUY":
                stop_loss = entry_price * 0.98  # 2% below entry
                take_profit = entry_price * 1.04  # 4% above entry

                if current_price <= stop_loss or current_price >= take_profit:
                    return True

        return crossover_exit
