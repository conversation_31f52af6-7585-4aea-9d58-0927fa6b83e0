import pandas as pd
from engine.core.strategy.istrategy import IStrategy

class Strategy(IStrategy):
    def __init__(self, config):
        super().__init__(config)
        self.fast_period = config.get("fast_period", 5)
        self.slow_period = config.get("slow_period", 20)

    def populate_indicators(self, df: pd.DataFrame):
        df["EMA_fast"] = df["close"].ewm(span=self.fast_period).mean()
        df["EMA_slow"] = df["close"].ewm(span=self.slow_period).mean()
        return df

    def entry_signal(self, df: pd.DataFrame):
        if len(df) < 2:
            return False

        prev, curr = df.iloc[-2], df.iloc[-1]
        return prev["EMA_fast"] < prev["EMA_slow"] and curr["EMA_fast"] > curr["EMA_slow"]

    def exit_signal(self, df: pd.DataFrame, ltp: float):
        if len(df) < 2:
            return False

        prev, curr = df.iloc[-2], df.iloc[-1]
        return prev["EMA_fast"] > prev["EMA_slow"] and curr["EMA_fast"] < curr["EMA_slow"]
