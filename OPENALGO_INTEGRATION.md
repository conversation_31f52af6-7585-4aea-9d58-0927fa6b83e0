# ZenStrato -> OpenAlgo Integration Guide

## Overview

ZenStrato now acts as a **Strategy Engine** that stores strategies and configurations, then uses **OpenAlgo (TradePilot) API** to place orders instead of connecting directly to brokers.

## Architecture Flow

```
ZenStrato Strategy Engine -> OpenAlgo API -> TradePilot -> Broker
```

### Previous Flow (Direct Broker Connection)
```
ZenStrato -> openalgo library -> Broker API
```

### New Flow (OpenAlgo Integration)
```
ZenStrato -> HTTP API calls -> TradePilot -> openalgo library -> Broker API
```

## Configuration Changes

### Strategy Config (config/strategy_name.json)

**New Required Fields:**
```json
{
  "openalgo_api_key": "your_openalgo_api_key_here",
  "openalgo_host": "http://127.0.0.1:5000",
  "use_openalgo": true,
  "exchange": "NSE",
  "price_type": "MARKET",
  "timeframe": "1"
}
```

**Removed Fields:**
- `api_key` (direct broker API key)
- `host` (direct broker host)
- `ws_url` (direct broker websocket)

## New Components

### 1. OpenAlgoClient (`engine/core/data/openalgo_client.py`)
- Replaces direct broker clients
- Makes HTTP requests to OpenAlgo API endpoints
- Handles order placement, market data, and account information

### 2. Strategy Health Monitor (`engine/utility/strategy_health_monitor.py`)
- Monitors strategy execution health
- Tracks trades, errors, and performance
- Provides health scores and status information

### 3. Enhanced ZenStrato API (`zen_inject/zenstrato_api.py`)
New endpoints:
- `GET /zenstrato/health` - Overall system health
- `GET /zenstrato/strategy/<name>/health` - Strategy-specific health
- `GET /zenstrato/strategies` - List all strategies
- `POST /zenstrato/openalgo/test` - Test OpenAlgo connection
- `POST /zenstrato/strategy/<name>/start` - Start strategy
- `POST /zenstrato/strategy/<name>/stop` - Stop strategy

## Setup Instructions

### 1. Update Strategy Configuration
```bash
# Edit your strategy config file
nano config/ema_crossover.json
```

Update with OpenAlgo settings:
```json
{
  "strategy_name": "ema_crossover",
  "symbol": "NSE:INFY",
  "exchange": "NSE",
  "use_openalgo": true,
  "openalgo_api_key": "your_api_key_from_tradepilot",
  "openalgo_host": "http://127.0.0.1:5000"
}
```

### 2. Get OpenAlgo API Key from TradePilot
1. Start TradePilot: `python main.py --tradepilot`
2. Open TradePilot web interface: http://127.0.0.1:5000
3. Go to API section and generate/copy your API key
4. Update the strategy config with this API key

### 3. Test the Integration
```bash
# Run integration test
python test_integration.py
```

### 4. Start ZenStrato Services
```bash
# Terminal 1: Start TradePilot
python main.py --tradepilot

# Terminal 2: Start ZenStrato webapp
python webapp.py

# Terminal 3: Run strategy
python main.py --strategy ema_crossover
```

## API Usage Examples

### Test OpenAlgo Connection
```bash
curl -X POST http://127.0.0.1:5400/zenstrato/openalgo/test \
  -H "X-API-KEY: your_zenstrato_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "openalgo_host": "http://127.0.0.1:5000",
    "openalgo_api_key": "your_openalgo_api_key"
  }'
```

### Check Strategy Health
```bash
curl -X GET http://127.0.0.1:5400/zenstrato/strategy/ema_crossover/health \
  -H "X-API-KEY: your_zenstrato_api_key"
```

### List All Strategies
```bash
curl -X GET http://127.0.0.1:5400/zenstrato/strategies \
  -H "X-API-KEY: your_zenstrato_api_key"
```

## Benefits of This Architecture

1. **Separation of Concerns**: ZenStrato focuses on strategy logic, TradePilot handles broker connectivity
2. **Centralized Order Management**: All orders go through TradePilot's interface
3. **Better Monitoring**: Enhanced health monitoring and status tracking
4. **Flexibility**: Easy to switch between different brokers through TradePilot
5. **API Analyzer Mode**: Test strategies without live trading through TradePilot's analyzer

## Troubleshooting

### Common Issues

1. **OpenAlgo Connection Failed**
   - Ensure TradePilot is running on port 5000
   - Verify OpenAlgo API key is correct
   - Check firewall settings

2. **Strategy Not Starting**
   - Verify config file has `use_openalgo: true`
   - Check OpenAlgo API key in config
   - Ensure TradePilot is authenticated with broker

3. **Orders Not Placing**
   - Check TradePilot broker connection
   - Verify sufficient funds in broker account
   - Check symbol format (e.g., "NSE:INFY")

### Debug Commands
```bash
# Check ZenStrato health
curl -X GET http://127.0.0.1:5400/zenstrato/health \
  -H "X-API-KEY: your_zenstrato_api_key"

# Test OpenAlgo connection
python test_integration.py

# Check TradePilot status
curl -X GET http://127.0.0.1:5000/api/v1/funds \
  -H "Authorization: Bearer your_openalgo_api_key"
```

## Migration from Direct Broker Connection

If you have existing strategies using direct broker connection:

1. **Backup** your current config files
2. **Update** config files with OpenAlgo settings
3. **Set** `use_openalgo: true` in config
4. **Remove** old broker API keys from config
5. **Test** with the integration test script
6. **Start** TradePilot before running strategies

The system maintains backward compatibility - strategies without `use_openalgo: true` will continue using direct broker connections.
