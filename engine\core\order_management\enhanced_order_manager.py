import asyncio
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from enum import Enum
import uuid

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class OrderStatus(Enum):
    PENDING = "pending"
    PLACED = "placed"
    FILLED = "filled"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY = "retry"

class EnhancedOrderManager:
    """
    Enhanced order manager with Redis persistence, retry logic, and parallel processing
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("EnhancedOrderManager")
        
        # Redis setup (optional)
        self.redis_client = None
        if REDIS_AVAILABLE and config.get("use_redis", False):
            try:
                self.redis_client = redis.Redis(
                    host=config.get("redis_host", "localhost"),
                    port=config.get("redis_port", 6379),
                    db=config.get("redis_db", 0),
                    decode_responses=True
                )
                self.redis_client.ping()
                self.logger.info("Redis connected successfully")
            except Exception as e:
                self.logger.warning(f"Redis connection failed: {e}, using memory storage")
                self.redis_client = None
        
        # In-memory fallback
        self.orders = {}
        self.failed_orders = {}
        self.retry_queue = asyncio.Queue()
        
        # Retry configuration
        self.max_retries = config.get("max_order_retries", 3)
        self.retry_delay = config.get("retry_delay_seconds", 5)
        self.retry_backoff = config.get("retry_backoff_multiplier", 2)
        
        # Parallel processing
        self.max_concurrent_orders = config.get("max_concurrent_orders", 5)
        self.order_semaphore = asyncio.Semaphore(self.max_concurrent_orders)
        
        # Order tracking
        self.order_stats = {
            "total_orders": 0,
            "successful_orders": 0,
            "failed_orders": 0,
            "retry_attempts": 0
        }
    
    async def place_order_with_retry(self, client, order_type: str, quantity: int, 
                                   strategy_name: str, symbol: str, price: float = None) -> Dict[str, Any]:
        """
        Place order with automatic retry and error handling
        """
        order_id = str(uuid.uuid4())
        order_data = {
            "order_id": order_id,
            "strategy_name": strategy_name,
            "symbol": symbol,
            "order_type": order_type,
            "quantity": quantity,
            "price": price,
            "status": OrderStatus.PENDING.value,
            "created_at": datetime.now().isoformat(),
            "retry_count": 0,
            "last_error": None
        }
        
        # Store order
        await self._store_order(order_data)
        
        # Use semaphore for parallel processing control
        async with self.order_semaphore:
            result = await self._execute_order_with_retry(client, order_data)
        
        return result
    
    async def _execute_order_with_retry(self, client, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute order with retry logic
        """
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.info(f"Placing order {order_data['order_id']}, attempt {attempt + 1}")
                
                # Update order status
                order_data["status"] = OrderStatus.PENDING.value
                order_data["retry_count"] = attempt
                await self._store_order(order_data)
                
                # Place order via TradePilot
                response = client.place_order(order_data["order_type"], order_data["quantity"])
                
                # Process TradePilot response
                if response.get("status") == "success":
                    # Success - update order
                    order_data.update({
                        "status": OrderStatus.PLACED.value,
                        "tradepilot_order_id": response.get("orderid"),
                        "placed_at": datetime.now().isoformat(),
                        "tradepilot_response": response
                    })
                    
                    await self._store_order(order_data)
                    self.order_stats["successful_orders"] += 1
                    
                    self.logger.info(f"Order {order_data['order_id']} placed successfully: {response.get('orderid')}")
                    
                    # Start order monitoring
                    asyncio.create_task(self._monitor_order_status(client, order_data))
                    
                    return {
                        "status": "success",
                        "order_id": order_data["order_id"],
                        "tradepilot_order_id": response.get("orderid"),
                        "message": "Order placed successfully"
                    }
                
                else:
                    # TradePilot returned error
                    error_msg = response.get("message", "Unknown error from TradePilot")
                    order_data["last_error"] = error_msg
                    
                    # Check if error is retryable
                    if self._is_retryable_error(response):
                        if attempt < self.max_retries:
                            delay = self.retry_delay * (self.retry_backoff ** attempt)
                            self.logger.warning(f"Order {order_data['order_id']} failed (retryable): {error_msg}. Retrying in {delay}s")
                            
                            order_data["status"] = OrderStatus.RETRY.value
                            await self._store_order(order_data)
                            self.order_stats["retry_attempts"] += 1
                            
                            await asyncio.sleep(delay)
                            continue
                    
                    # Non-retryable error or max retries reached
                    order_data["status"] = OrderStatus.FAILED.value
                    order_data["failed_at"] = datetime.now().isoformat()
                    await self._store_order(order_data)
                    await self._store_failed_order(order_data)
                    
                    self.order_stats["failed_orders"] += 1
                    
                    self.logger.error(f"Order {order_data['order_id']} failed permanently: {error_msg}")
                    
                    return {
                        "status": "failed",
                        "order_id": order_data["order_id"],
                        "error": error_msg,
                        "retry_count": attempt
                    }
                    
            except Exception as e:
                error_msg = f"Exception during order placement: {str(e)}"
                order_data["last_error"] = error_msg
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (self.retry_backoff ** attempt)
                    self.logger.warning(f"Order {order_data['order_id']} exception: {error_msg}. Retrying in {delay}s")
                    
                    order_data["status"] = OrderStatus.RETRY.value
                    await self._store_order(order_data)
                    self.order_stats["retry_attempts"] += 1
                    
                    await asyncio.sleep(delay)
                    continue
                
                # Max retries reached
                order_data["status"] = OrderStatus.FAILED.value
                order_data["failed_at"] = datetime.now().isoformat()
                await self._store_order(order_data)
                await self._store_failed_order(order_data)
                
                self.order_stats["failed_orders"] += 1
                
                self.logger.error(f"Order {order_data['order_id']} failed after {self.max_retries} retries: {error_msg}")
                
                return {
                    "status": "failed",
                    "order_id": order_data["order_id"],
                    "error": error_msg,
                    "retry_count": self.max_retries
                }
        
        # Should never reach here
        return {"status": "failed", "error": "Unexpected error"}
    
    async def _monitor_order_status(self, client, order_data: Dict[str, Any]):
        """
        Monitor order status until filled or cancelled
        """
        tradepilot_order_id = order_data.get("tradepilot_order_id")
        if not tradepilot_order_id:
            return
        
        max_monitoring_time = 300  # 5 minutes
        start_time = time.time()
        
        while (time.time() - start_time) < max_monitoring_time:
            try:
                # Check order status from TradePilot
                status_response = client.orderstatus(tradepilot_order_id)
                
                if status_response.get("status") == "success":
                    order_status = status_response.get("data", {}).get("status", "unknown")
                    
                    if order_status.lower() in ["complete", "filled"]:
                        order_data["status"] = OrderStatus.FILLED.value
                        order_data["filled_at"] = datetime.now().isoformat()
                        order_data["fill_details"] = status_response.get("data", {})
                        
                        await self._store_order(order_data)
                        
                        self.logger.info(f"Order {order_data['order_id']} filled successfully")
                        break
                    
                    elif order_status.lower() in ["cancelled", "rejected"]:
                        order_data["status"] = OrderStatus.CANCELLED.value
                        order_data["cancelled_at"] = datetime.now().isoformat()
                        order_data["cancel_reason"] = status_response.get("data", {}).get("message", "Unknown")
                        
                        await self._store_order(order_data)
                        
                        self.logger.warning(f"Order {order_data['order_id']} was cancelled/rejected")
                        break
                
                # Wait before next check
                await asyncio.sleep(5)
                
            except Exception as e:
                self.logger.error(f"Error monitoring order {order_data['order_id']}: {e}")
                await asyncio.sleep(10)
        
        # Monitoring timeout
        if order_data["status"] == OrderStatus.PLACED.value:
            self.logger.warning(f"Order {order_data['order_id']} monitoring timeout")
    
    def _is_retryable_error(self, response: Dict[str, Any]) -> bool:
        """
        Determine if an error is retryable
        """
        error_msg = response.get("message", "").lower()
        
        # Retryable errors
        retryable_errors = [
            "network error",
            "timeout",
            "server error",
            "temporary",
            "rate limit",
            "connection",
            "503",
            "502",
            "500"
        ]
        
        # Non-retryable errors
        non_retryable_errors = [
            "insufficient funds",
            "invalid symbol",
            "market closed",
            "invalid quantity",
            "unauthorized",
            "forbidden"
        ]
        
        # Check non-retryable first
        for error in non_retryable_errors:
            if error in error_msg:
                return False
        
        # Check retryable
        for error in retryable_errors:
            if error in error_msg:
                return True
        
        # Default: retry unknown errors
        return True
    
    async def _store_order(self, order_data: Dict[str, Any]):
        """
        Store order data (Redis or memory)
        """
        order_id = order_data["order_id"]
        
        if self.redis_client:
            try:
                # Store in Redis with expiration
                key = f"zenstrato:order:{order_id}"
                self.redis_client.setex(key, 86400, json.dumps(order_data))  # 24 hours
                
                # Add to strategy orders list
                strategy_key = f"zenstrato:strategy:{order_data['strategy_name']}:orders"
                self.redis_client.lpush(strategy_key, order_id)
                self.redis_client.expire(strategy_key, 86400)
                
            except Exception as e:
                self.logger.error(f"Redis storage error: {e}")
                # Fallback to memory
                self.orders[order_id] = order_data
        else:
            # Memory storage
            self.orders[order_id] = order_data
    
    async def _store_failed_order(self, order_data: Dict[str, Any]):
        """
        Store failed order for analysis
        """
        order_id = order_data["order_id"]
        
        if self.redis_client:
            try:
                key = f"zenstrato:failed_order:{order_id}"
                self.redis_client.setex(key, 604800, json.dumps(order_data))  # 7 days
            except Exception as e:
                self.logger.error(f"Redis failed order storage error: {e}")
                self.failed_orders[order_id] = order_data
        else:
            self.failed_orders[order_id] = order_data
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Get order status
        """
        if self.redis_client:
            try:
                key = f"zenstrato:order:{order_id}"
                data = self.redis_client.get(key)
                if data:
                    return json.loads(data)
            except Exception as e:
                self.logger.error(f"Redis get error: {e}")
        
        return self.orders.get(order_id)
    
    async def get_strategy_orders(self, strategy_name: str) -> List[Dict[str, Any]]:
        """
        Get all orders for a strategy
        """
        orders = []
        
        if self.redis_client:
            try:
                strategy_key = f"zenstrato:strategy:{strategy_name}:orders"
                order_ids = self.redis_client.lrange(strategy_key, 0, -1)
                
                for order_id in order_ids:
                    order_data = await self.get_order_status(order_id)
                    if order_data:
                        orders.append(order_data)
            except Exception as e:
                self.logger.error(f"Redis strategy orders error: {e}")
        else:
            # Memory search
            for order_data in self.orders.values():
                if order_data.get("strategy_name") == strategy_name:
                    orders.append(order_data)
        
        return orders
    
    def get_order_statistics(self) -> Dict[str, Any]:
        """
        Get order statistics
        """
        return {
            "total_orders": self.order_stats["total_orders"],
            "successful_orders": self.order_stats["successful_orders"],
            "failed_orders": self.order_stats["failed_orders"],
            "retry_attempts": self.order_stats["retry_attempts"],
            "success_rate": (self.order_stats["successful_orders"] / max(1, self.order_stats["total_orders"])) * 100,
            "concurrent_limit": self.max_concurrent_orders,
            "redis_enabled": self.redis_client is not None
        }
