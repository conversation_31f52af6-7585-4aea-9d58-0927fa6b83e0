# routes/events.py
from flask import Blueprint, request, jsonify
import time
import sys
import os

# Add the parent directory to the path to import ZenStrato modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

events_bp = Blueprint("events_bp", __name__)
event_log = []

@events_bp.route("/event", methods=["POST"])
def receive_event():
    data = request.json
    data["timestamp"] = time.time()
    event_log.append(data)

    # Handle mode change events
    if data.get("event") == "mode_change":
        handle_mode_change(data.get("payload", {}))

    return jsonify({"status": "received"})

def handle_mode_change(payload):
    """Handle mode change events from TradePilot"""
    try:
        from engine.utility.mode_manager import mode_manager

        raw_mode = payload.get("mode")

        # Convert numeric mode to string format
        if raw_mode == 0 or raw_mode == "0":
            new_mode = "live"
        elif raw_mode == 1 or raw_mode == "1":
            new_mode = "api-analyser"
        elif raw_mode in ["live", "api-analyser"]:
            new_mode = raw_mode
        else:
            print(f"⚠️ [WEBAPP] Invalid mode received: {raw_mode}")
            return

        print(f"🔄 [WEBAPP] Mode change received: {raw_mode} → {new_mode}")
        mode_manager.set_mode(new_mode, source="tradepilot_event")
        print(f"✅ [WEBAPP] ZenStrato mode updated to: {new_mode}")

    except Exception as e:
        print(f"❌ [WEBAPP] Error handling mode change: {e}")

@events_bp.route("/events", methods=["GET"])
def get_events():
    return jsonify(list(reversed(event_log[-100:])))

@events_bp.route("/mode", methods=["GET"])
def get_current_mode():
    """Get current ZenStrato mode"""
    try:
        from engine.utility.mode_manager import mode_manager
        return jsonify({
            "mode": mode_manager.get_mode(),
            "timestamp": time.time()
        })
    except Exception as e:
        return jsonify({
            "error": str(e),
            "mode": "unknown"
        }), 500
