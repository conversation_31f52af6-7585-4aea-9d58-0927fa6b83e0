import pandas as pd
import numpy as np
from engine.core.strategy.istrategy import IStrategy
from engine.utility.resource_monitor import profile_function
import logging
import time

class Strategy(IStrategy):
    """
    Test strategy for YESBANK - triggers buy/sell signals frequently for testing
    
    Entry Signals:
    - BUY: Every 2 minutes (for testing)
    - SELL: 30 seconds after BUY (for testing)
    
    This strategy is designed to test the complete order flow system
    """
    
    def __init__(self, config):
        super().__init__(config)
        self.config = config
        self.logger = logging.getLogger("TestBuySellYesBankStrategy")

        # Timeframe configuration
        self.timeframe = config.get("timeframe", "1m")
        self.symbol = config.get("symbol", "UNKNOWN")

        # Test parameters - designed to trigger signals
        self.last_buy_time = 0
        self.last_sell_time = 0
        self.buy_interval = 120  # Buy every 2 minutes
        self.sell_interval = 30  # Sell 30 seconds after buy
        self.position_active = False
        self.entry_price = None
        
        # Risk management (same as normal strategy)
        self.stop_loss_pct = config.get("sl_multiplier", 2.0)
        self.take_profit_pct = config.get("tp_multiplier", 4.0)

        # Counter for alternating signals
        self.signal_counter = 0

        self.logger.info(f"TEST Strategy initialized for {self.symbol} on {self.timeframe} timeframe")
        self.logger.info(f"🧪 TEST MODE: Will generate BUY signals every {self.buy_interval}s, SELL after {self.sell_interval}s")

    @profile_function("populate_indicators")
    def populate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Simple indicators for testing - just add a test column
        """
        if df.empty:
            return df
        
        # Work with copy to avoid modifying original
        df = df.copy()
        
        # Add simple test indicators
        df['test_signal'] = 1  # Always ready for signals
        df['price_change'] = df['close'].pct_change()
        
        return df
    
    def entry_signal(self, df: pd.DataFrame) -> bool:
        """
        Test entry signal - triggers every 2 minutes for testing
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            current_time = time.time()
            current_price = df['close'].iloc[-1]
            
            # Log current status every 30 seconds
            if int(current_time) % 30 == 0:
                time_since_last_buy = current_time - self.last_buy_time
                self.logger.info(f"🧪 TEST STATUS: Price=₹{current_price:.2f}, Time since last buy={time_since_last_buy:.0f}s, Position={self.position_active}")
            
            # Only buy if we don't have an active position
            if not self.position_active:
                # Check if enough time has passed since last buy
                if (current_time - self.last_buy_time) >= self.buy_interval:
                    self.last_buy_time = current_time
                    self.position_active = True
                    self.entry_price = current_price
                    self.signal_counter += 1
                    
                    self.logger.info(f"🧪 TEST BUY SIGNAL #{self.signal_counter}: Price=₹{current_price:.2f}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in test entry_signal: {e}")
            return False
    
    def exit_signal(self, df: pd.DataFrame, current_price: float, entry_price: float = None, position_type: str = "BUY") -> bool:
        """
        Test exit signal - triggers 30 seconds after entry
        """
        if df.empty or not self.position_active:
            return False
        
        try:
            current_time = time.time()
            
            # Exit after 30 seconds for testing
            if (current_time - self.last_buy_time) >= self.sell_interval:
                self.position_active = False
                self.logger.info(f"🧪 TEST SELL SIGNAL: Price=₹{current_price:.2f}, Held for {self.sell_interval}s")
                return True
            
            # Also check stop loss / take profit (same as normal strategy)
            if entry_price and self.entry_price:
                if position_type == "BUY":
                    stop_loss = self.entry_price * (1 - self.stop_loss_pct / 100)
                    take_profit = self.entry_price * (1 + self.take_profit_pct / 100)
                    
                    if current_price <= stop_loss:
                        self.position_active = False
                        self.logger.info(f"🧪 TEST STOP LOSS: {current_price} <= {stop_loss}")
                        return True
                    
                    if current_price >= take_profit:
                        self.position_active = False
                        self.logger.info(f"🧪 TEST TAKE PROFIT: {current_price} >= {take_profit}")
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in test exit_signal: {e}")
            return False
    
    def get_strategy_info(self) -> dict:
        """
        Get test strategy information
        """
        return {
            "name": "TEST_BUY_SELL_YESBANK",
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "indicators": ["test_signal", "price_change"],
            "parameters": {
                "timeframe": self.timeframe,
                "buy_interval": self.buy_interval,
                "sell_interval": self.sell_interval,
                "stop_loss_pct": self.stop_loss_pct,
                "take_profit_pct": self.take_profit_pct
            },
            "test_status": {
                "signal_counter": self.signal_counter,
                "position_active": self.position_active,
                "last_buy_time": self.last_buy_time,
                "entry_price": self.entry_price
            },
            "description": "Test strategy that generates predictable buy/sell signals for system validation"
        }
    
    def optimize_for_server(self):
        """
        Server-specific optimizations for test strategy
        """
        # Reset test state
        self.last_buy_time = 0
        self.last_sell_time = 0
        self.position_active = False
        self.entry_price = None
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info("🧪 Test strategy optimized for server resources")
