import os
import json
import importlib.util
from engine.core.strategy.strategy_wrapper import validate_strategy

def load_strategy(strategy_name: str):
    """
    strategy_name: 'ema_crossover'
    Loads:
    - strategy/ema_crossover.py
    - config/ema_crossover.json
    """
    base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))

    strategy_file = os.path.join(base_path, "strategy", f"{strategy_name}.py")
    config_file = os.path.join(base_path, "config", f"{strategy_name}.json")

    if not os.path.exists(strategy_file):
        raise FileNotFoundError(f"Missing strategy file: {strategy_file}")
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"Missing config file: {config_file}")

    with open(config_file, "r") as f:
        config = json.load(f)

    spec = importlib.util.spec_from_file_location(strategy_name, strategy_file)
    strategy_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(strategy_module)

    if not hasattr(strategy_module, "Strategy"):
        raise AttributeError(f"'Strategy' class not found in {strategy_file}")

    strategy = strategy_module.Strategy(config)
    validate_strategy(strategy)

    return strategy, config
