# engine\core\data\client_factory.py

def get_client(config):
    """
    Factory function to create appropriate client based on configuration

    Priority:
    1. If use_openalgo is True -> OpenAlgoClient (communicates with TradePilot)
    2. If websocket is True -> WebSocketClient (direct broker connection)
    3. Default -> APIClient (direct broker connection)
    """

    # Check if we should use OpenAlgo (TradePilot) API
    if config.get("use_openalgo", False):
        from .openalgo_client import OpenAlgoClient
        return OpenAlgoClient(config)

    # Legacy direct broker connection (for backward compatibility)
    elif config.get("websocket", False):
        from .websocket_client import WebSocketClient
        return WebSocketClient(config)
    else:
        from .api_client import APIClient
        return APIClient(config)
