<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>ZenStrato Dashboard</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@2.51.6/dist/full.css" rel="stylesheet" />
</head>
<body class="bg-base-100">
  <div class="container mx-auto p-4">
    <h1 class="text-3xl font-bold mb-4">ZenStrato • OpenAlgo Style</h1>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="card bg-base-200 p-4 shadow-lg">
        <h2 class="font-semibold text-lg">📡 Current Mode</h2>
        <p id="current-mode" class="text-2xl mt-2">–</p>
      </div>
      <div class="card bg-base-200 p-4 shadow-lg">
        <h2 class="font-semibold text-lg">🛠️ Active Strategy</h2>
        <p id="current-strategy" class="text-2xl mt-2">–</p>
      </div>
      <div class="card bg-base-200 p-4 shadow-lg">
        <h2 class="font-semibold text-lg">📋 Event Feed</h2>
        <ul id="event-feed" class="mt-2 max-h-40 overflow-y-auto"></ul>
      </div>
    </div>

    <div class="flex space-x-4 mb-8">
      <button class="btn btn-primary" onclick="changeMode(1)">Switch to Analyze</button>
      <button class="btn btn-secondary" onclick="changeMode(0)">Switch to Live</button>
    </div>

    <hr class="my-6" />

    <div class="card bg-base-200 p-4 shadow-lg">
      <h2 class="font-semibold text-lg">📈 Event Log</h2>
      <ul id="event-log" class="mt-2 list-disc ml-5 max-h-96 overflow-y-auto"></ul>
    </div>
  </div>

  <script>
    let lastTs = 0;

    async function refresh() {
      // Fetch latest events
      const res = await fetch("/zenstrato/events");
      const data = await res.json();

      // Update mode & strategy
      const feed = document.getElementById("event-feed");
      feed.innerHTML = '';
      data.slice(0, 5).forEach(e => {
        const li = document.createElement("li");
        li.innerHTML = `<strong>${e.event}</strong>: ${JSON.stringify(e.payload)}`;
        feed.prepend(li);
      });
      const last = data[0];
      if (last) {
        if (last.event === "mode_change") {
          document.getElementById("current-mode")
            .textContent = last.payload.mode ? "Analyze" : "Live";
        }
      }
      refreshLog(data);
    }

    async function refreshLog(data) {
      const log = document.getElementById("event-log");
      log.innerHTML = '';
      data.forEach(e => {
        const dt = new Date(e.timestamp);
        const item = document.createElement("li");
        item.innerHTML = `[${dt.toLocaleTimeString()}] <strong>${e.event}</strong> – ${JSON.stringify(e.payload)}`;
        log.prepend(item);
      });
    }

    async function changeMode(val) {
      await fetch(`/settings/analyze-mode/${val}`, {method: "POST"});
      setTimeout(refresh, 500);
    }

    setInterval(refresh, 2000);
    window.onload = refresh;
  </script>
</body>
</html>
