{"_config_info": {"version": "2.0", "description": "Complete Enhanced ZenStrato Engine Configuration Template", "last_updated": "2025-07-01", "documentation": "All parameters are customizable through this config file"}, "strategy_name": "enhanced_strategy_template", "symbol": "SYMBOL_NAME", "exchange": "NSE", "product": "MIS", "price_type": "MARKET", "timeframe": "1m", "capital_management": {"total_capital": 10000, "capital_per_trade": 5000, "max_capital_per_symbol": 8000, "reserve_capital": 2000, "capital_allocation_method": "FIXED", "description": "Capital allocation and management settings"}, "quantity_management": {"fixed_quantity": null, "quantity_calculation_method": "CAPITAL_BASED", "min_quantity": 1, "max_quantity": 10000, "quantity_multiplier": 1.0, "lot_size": 1, "round_to_lot_size": false, "description": "Quantity calculation and limits"}, "timeframe_config": {"primary_timeframe": "1m", "supported_timeframes": ["1m", "3m", "5m", "15m", "30m", "1h", "1d"], "data_range_hours": 2, "auto_adjust_indicators": true, "fetch_interval_override": null, "description": "Timeframe used for data fetching, indicator calculation, and signal generation"}, "enhanced_order_management": {"use_redis": false, "redis_host": "localhost", "redis_port": 6379, "redis_db": 0, "max_order_retries": 3, "retry_delay_seconds": 2, "retry_backoff_multiplier": 1.5, "max_concurrent_orders": 5}, "parallel_processing": {"max_concurrent_strategies": 2, "max_worker_threads": 3, "signal_cooldown_seconds": 30, "loop_interval_seconds": 10}, "optimization": {"enable_caching": true, "cache_size": 300, "memory_cleanup_interval": 600, "max_api_calls_per_minute": 60}, "risk_management": {"max_position_size": 0.1, "daily_loss_limit": 500, "max_drawdown_pct": 5}}