from .istrategy import IStrategy

def validate_strategy(strategy):
    if not isinstance(strategy, IStrategy):
        raise TypeError("Strategy must inherit from IStrategy")

    required_methods = ["populate_indicators", "entry_signal", "exit_signal"]
    for method in required_methods:
        if not callable(getattr(strategy, method, None)):
            raise AttributeError(f"Strategy is missing required method: {method}")

    return True
