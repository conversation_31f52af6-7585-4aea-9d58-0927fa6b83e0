import os
from datetime import datetime

LOG_FILE = "trades_log.csv"

def log_trade(trade: dict):
    headers = ["time", "strategy", "symbol", "entry", "exit", "qty", "status", "order_id", "error"]
    file_exists = os.path.exists(LOG_FILE)

    with open(LOG_FILE, "a") as f:
        if not file_exists:
            f.write(",".join(headers) + "\n")

        row = [
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            trade.get("strategy", ""),
            trade.get("symbol", ""),
            str(trade.get("entry_price", "")),
            str(trade.get("exit_price", "")),
            str(trade.get("qty", "")),
            trade.get("status", ""),
            trade.get("order_id", ""),
            trade.get("error", "")
        ]
        f.write(",".join(row) + "\n")
