{"strategy_name": "yesbank_test", "symbol": "YESBANK", "exchange": "NSE", "product": "MIS", "timeframe": "1m", "ema_parameters": {"fast_period": 5, "slow_period": 10, "description": "EMA crossover periods optimized for YESBANK"}, "capital_management": {"total_capital": 100, "capital_per_trade": 50, "capital_allocation_method": "CAPITAL_BASED"}, "quantity_management": {"quantity_calculation_method": "FIXED", "fixed_quantity": 1, "min_quantity": 1, "max_quantity": 3}, "websocket_config": {"enable_websocket": false, "websocket_url": "ws://127.0.0.1:5000/ws", "real_time_data": true, "ltp_streaming": true, "description": "WebSocket configuration for real-time data"}, "broker_config": {"openalgo_api_key": "406a16fcd1e6c652c491fcec23fac6936be503b93cf0aa6cd49029f259250203", "openalgo_host": "http://127.0.0.1:5000", "openalgo_ws_url": "ws://127.0.0.1:5000/ws", "use_openalgo": true, "connection_timeout": 30}, "order_management": {"order_type": "MARKET", "price_type": "MARKET", "validity": "DAY", "order_tag": "YESBANK_Test", "order_timeout": 30}, "risk_management": {"sl_multiplier": 0.5, "tp_multiplier": 1.0, "max_position_size": 0.1, "daily_loss_limit": 50, "stop_loss_percentage": 2.0, "take_profit_percentage": 4.0}, "enhanced_order_management": {"max_order_retries": 3, "retry_delay_seconds": 2, "retry_backoff_multiplier": 1.5, "max_concurrent_orders": 2}, "optimization": {"enable_caching": true, "cache_size": 300, "memory_cleanup_interval": 600, "max_api_calls_per_minute": 60}, "testing_config": {"test_mode": "API_ANALYZER", "test_duration_days": 3, "max_trades_per_day": 5, "test_start_date": "2025-07-03", "monitoring_interval": "1m", "log_level": "INFO"}, "legacy_params": {"capital": 100, "fast_period": 5, "slow_period": 10, "websocket": false, "parallel_orders": false, "max_concurrent_trades": 1}}