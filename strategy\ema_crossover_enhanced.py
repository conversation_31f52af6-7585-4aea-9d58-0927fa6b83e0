import pandas as pd
import numpy as np
from engine.core.strategy.istrategy import IStrategy
from engine.utility.resource_monitor import profile_function
import logging

class Strategy(IStrategy):
    """
    Enhanced EMA Crossover Strategy for ZenStrato Engine
    
    Entry Signals:
    - BUY: Fast EMA crosses above Slow EMA (bullish crossover)
    - SELL: Fast EMA crosses below Slow EMA (bearish crossover)
    
    Features:
    - Configurable EMA periods through config.json
    - WebSocket support for real-time data
    - Enhanced order management with retry logic
    - Performance optimization and caching
    """
    
    def __init__(self, config):
        super().__init__(config)
        self.config = config
        self.logger = logging.getLogger("EMACrossoverStrategy")
        
        # EMA parameters from config
        self.fast_period = config.get("fast_period", 5)
        self.slow_period = config.get("slow_period", 10)
        
        # Timeframe configuration
        self.timeframe = config.get("timeframe", "1m")
        self.symbol = config.get("symbol", "BHEL")
        
        # Risk management
        self.stop_loss_pct = config.get("sl_multiplier", 2.0)
        self.take_profit_pct = config.get("tp_multiplier", 4.0)
        
        # Performance optimization - store last values
        self.last_fast_ema = None
        self.last_slow_ema = None
        self.last_crossover = False
        self.last_crossunder = False
        
        # Position tracking
        self.position = 0
        
        self.logger.info(f"EMA Crossover Strategy initialized: {self.fast_period}/{self.slow_period} on {self.symbol} ({self.timeframe})")
        
    @profile_function("populate_indicators")
    def populate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate EMA indicators and crossover signals efficiently
        """
        if df.empty or len(df) < max(self.fast_period, self.slow_period):
            return df
        
        # Work with copy to avoid modifying original
        df = df.copy()
        
        # Calculate EMAs using pandas ewm (exponential weighted moving average)
        df['EMA_Fast'] = df['close'].ewm(span=self.fast_period, adjust=False).mean()
        df['EMA_Slow'] = df['close'].ewm(span=self.slow_period, adjust=False).mean()
        
        # Calculate crossover signals
        df['Crossover'] = self._calculate_crossover_signals(df)
        df['Crossunder'] = self._calculate_crossunder_signals(df)
        
        # Store last values for quick access
        if len(df) > 0:
            self.last_fast_ema = df['EMA_Fast'].iloc[-1]
            self.last_slow_ema = df['EMA_Slow'].iloc[-1]
            self.last_crossover = df['Crossover'].iloc[-1] if len(df) > 1 else False
            self.last_crossunder = df['Crossunder'].iloc[-1] if len(df) > 1 else False
        
        return df
    
    def _calculate_crossover_signals(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate bullish crossover signals (Fast EMA crosses above Slow EMA)
        """
        if len(df) < 2:
            return pd.Series([False] * len(df), index=df.index)
        
        fast_ema = df['EMA_Fast']
        slow_ema = df['EMA_Slow']
        
        # Previous values
        prev_fast = fast_ema.shift(1)
        prev_slow = slow_ema.shift(1)
        
        # Crossover: previous fast < previous slow AND current fast > current slow
        crossover = (prev_fast < prev_slow) & (fast_ema > slow_ema)
        
        return crossover.fillna(False)
    
    def _calculate_crossunder_signals(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate bearish crossover signals (Fast EMA crosses below Slow EMA)
        """
        if len(df) < 2:
            return pd.Series([False] * len(df), index=df.index)
        
        fast_ema = df['EMA_Fast']
        slow_ema = df['EMA_Slow']
        
        # Previous values
        prev_fast = fast_ema.shift(1)
        prev_slow = slow_ema.shift(1)
        
        # Crossunder: previous fast > previous slow AND current fast < current slow
        crossunder = (prev_fast > prev_slow) & (fast_ema < slow_ema)
        
        return crossunder.fillna(False)
    
    def entry_signal(self, df: pd.DataFrame) -> bool:
        """
        Check for entry signal (EMA crossover)
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            # Check if we have the required indicators
            required_cols = ['EMA_Fast', 'EMA_Slow', 'Crossover', 'Crossunder']
            if not all(col in df.columns for col in required_cols):
                return False
            
            # Get latest crossover signal (using -2 to avoid partial candle like OpenAlgo example)
            crossover = df['Crossover'].iloc[-2] if len(df) >= 2 else False
            crossunder = df['Crossunder'].iloc[-2] if len(df) >= 2 else False
            
            # BUY Signal: Fast EMA crosses above Slow EMA
            if crossover and self.position <= 0:
                fast_ema = df['EMA_Fast'].iloc[-2]
                slow_ema = df['EMA_Slow'].iloc[-2]
                
                self.logger.info(f"BUY signal: EMA crossover detected")
                self.logger.info(f"Fast EMA ({self.fast_period}): {fast_ema:.2f}")
                self.logger.info(f"Slow EMA ({self.slow_period}): {slow_ema:.2f}")
                return True
            
            # SELL Signal: Fast EMA crosses below Slow EMA
            elif crossunder and self.position >= 0:
                fast_ema = df['EMA_Fast'].iloc[-2]
                slow_ema = df['EMA_Slow'].iloc[-2]
                
                self.logger.info(f"SELL signal: EMA crossunder detected")
                self.logger.info(f"Fast EMA ({self.fast_period}): {fast_ema:.2f}")
                self.logger.info(f"Slow EMA ({self.slow_period}): {slow_ema:.2f}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in entry_signal: {e}")
            return False
    
    def exit_signal(self, df: pd.DataFrame, current_price: float, entry_price: float = None, position_type: str = "BUY") -> bool:
        """
        Check for exit signal (opposite crossover or stop-loss/take-profit)
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            # Check for opposite crossover signal
            crossover = df['Crossover'].iloc[-2] if len(df) >= 2 else False
            crossunder = df['Crossunder'].iloc[-2] if len(df) >= 2 else False
            
            # Exit BUY position on bearish crossover
            if position_type == "BUY" and crossunder:
                self.logger.info(f"EXIT BUY: Bearish crossover detected")
                return True
            
            # Exit SELL position on bullish crossover
            elif position_type == "SELL" and crossover:
                self.logger.info(f"EXIT SELL: Bullish crossover detected")
                return True
            
            # Stop loss / Take profit (if entry price provided)
            if entry_price:
                if position_type == "BUY":
                    stop_loss = entry_price * (1 - self.stop_loss_pct / 100)
                    take_profit = entry_price * (1 + self.take_profit_pct / 100)
                    
                    if current_price <= stop_loss:
                        self.logger.info(f"Stop loss triggered: {current_price} <= {stop_loss}")
                        return True
                    
                    if current_price >= take_profit:
                        self.logger.info(f"Take profit triggered: {current_price} >= {take_profit}")
                        return True
                
                elif position_type == "SELL":
                    stop_loss = entry_price * (1 + self.stop_loss_pct / 100)
                    take_profit = entry_price * (1 - self.take_profit_pct / 100)
                    
                    if current_price >= stop_loss:
                        self.logger.info(f"Stop loss triggered: {current_price} >= {stop_loss}")
                        return True
                    
                    if current_price <= take_profit:
                        self.logger.info(f"Take profit triggered: {current_price} <= {take_profit}")
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in exit_signal: {e}")
            return False
    
    def update_position(self, side: str, quantity: int):
        """
        Update position tracking (similar to OpenAlgo example)
        """
        if side.upper() == "BUY":
            self.position = quantity
        elif side.upper() == "SELL":
            self.position = -quantity
        else:
            self.position = 0
        
        self.logger.info(f"Position updated: {self.position}")
    
    def get_strategy_info(self) -> dict:
        """
        Get strategy information including EMA values and signals
        """
        return {
            "name": "EMA_CROSSOVER_ENHANCED",
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "indicators": ["EMA_Fast", "EMA_Slow"],
            "parameters": {
                "fast_period": self.fast_period,
                "slow_period": self.slow_period,
                "stop_loss_pct": self.stop_loss_pct,
                "take_profit_pct": self.take_profit_pct
            },
            "current_values": {
                "fast_ema": self.last_fast_ema,
                "slow_ema": self.last_slow_ema,
                "last_crossover": self.last_crossover,
                "last_crossunder": self.last_crossunder,
                "position": self.position
            },
            "signals": {
                "crossover_detected": self.last_crossover,
                "crossunder_detected": self.last_crossunder
            }
        }
    
    def optimize_for_server(self):
        """
        Server-specific optimizations
        """
        # Clear cached values to save memory
        self.last_fast_ema = None
        self.last_slow_ema = None
        self.last_crossover = False
        self.last_crossunder = False
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info("EMA Crossover strategy optimized for server resources")
