# ZenStrato OpenAlgo Order Placement Fix

## Issue Description
ZenStrato was encountering a `'price_type'` error when attempting to place orders through the OpenAlgo API:

```
❌ BUY failed: Exception during order placement: 'price_type'
```

## Root Cause Analysis
The issue was a parameter naming mismatch between:
- **ZenStrato Configuration**: Uses `price_type` (with underscore)
- **OpenAlgo API**: Expects `pricetype` (without underscore)

## Files Modified

### 1. `engine/core/data/openalgo_client.py`

**Changes Made:**
1. **Fixed parameter mapping** in `place_order()` method:
   ```python
   # Before (causing error)
   "pricetype": self.config.get("price_type", "MARKET")
   
   # After (working fix)
   price_type = (self.config.get("price_type") or 
                self.config.get("order_management", {}).get("price_type") or 
                "MARKET")
   "pricetype": price_type  # OpenAlgo API expects 'pricetype'
   ```

2. **Enhanced config handling** to support both flat and nested structures:
   ```python
   # Handle both config structures
   broker_config = config.get("broker_config", {})
   self.openalgo_api_key = (config.get("openalgo_api_key") or 
                           broker_config.get("openalgo_api_key"))
   ```

3. **Removed unused import** (`asyncio`)

## Configuration Structure Support

The fix now supports both configuration formats:

### Flat Structure (Legacy)
```json
{
  "openalgo_api_key": "your_key",
  "price_type": "MARKET"
}
```

### Nested Structure (Current)
```json
{
  "broker_config": {
    "openalgo_api_key": "your_key"
  },
  "order_management": {
    "price_type": "MARKET"
  }
}
```

## Testing

Created `test_order_placement.py` to verify the fix:
- ✅ OpenAlgo client creation
- ✅ Parameter mapping verification
- ✅ No more `'price_type'` errors

## Results

### Before Fix
```
❌ Order d23ff174-87af-4510-bf51-5f164ecf5076 failed after 3 retries: 
   Exception during order placement: 'price_type'
```

### After Fix
```
✅ Strategy running successfully
✅ HTTP requests to OpenAlgo API working
✅ No parameter-related errors
```

## OpenAlgo API Reference

According to [OpenAlgo Documentation](https://docs.openalgo.in/api-documentation/v1/orders-api/placeorder):

**Correct Parameter Name:** `pricetype` (not `price_type`)

**Example API Request:**
```json
{
  "apikey": "your_api_key",
  "strategy": "Test Strategy",
  "symbol": "BHEL",
  "action": "BUY",
  "exchange": "NSE",
  "pricetype": "MARKET",  // ← Correct parameter name
  "product": "MIS",
  "quantity": "1"
}
```

## Impact

This fix resolves the order placement failures in ZenStrato when using OpenAlgo/TradePilot integration, allowing strategies to execute trades successfully.

## Verification Steps

1. Run strategy: `python main.py --strategy ema_crossover`
2. Verify no `'price_type'` errors in logs
3. Confirm HTTP requests to OpenAlgo API are successful
4. Test order placement when signals are generated

---

**Status:** ✅ **RESOLVED**  
**Date:** 2025-07-03  
**Tested:** ✅ Verified working
