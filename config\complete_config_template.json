{"_config_info": {"version": "2.0", "description": "Complete Enhanced ZenStrato Engine Configuration Template", "last_updated": "2025-07-01", "documentation": "ALL parameters customizable through this config file", "author": "Enhanced ZenStrato Engine"}, "strategy_name": "complete_strategy_template", "symbol": "YESBANK", "exchange": "NSE", "product": "MIS", "timeframe": "1m", "broker_config": {"openalgo_api_key": "your_openalgo_api_key_here", "openalgo_host": "http://127.0.0.1:5000", "openalgo_ws_url": "ws://127.0.0.1:5000/ws", "use_openalgo": true, "broker_name": "TradePilot", "connection_timeout": 30, "retry_attempts": 3, "api_rate_limit": 60, "description": "Broker connection and API configuration"}, "capital_management": {"total_capital": 10000, "capital_per_trade": 5000, "max_capital_per_symbol": 8000, "reserve_capital": 2000, "capital_allocation_method": "PERCENTAGE", "capital_percentage_per_trade": 50, "compound_profits": true, "description": "Capital allocation and management settings"}, "quantity_management": {"quantity_calculation_method": "CAPITAL_BASED", "fixed_quantity": null, "min_quantity": 1, "max_quantity": 10000, "quantity_multiplier": 1.0, "lot_size": 1, "round_to_lot_size": false, "quantity_based_on": "AVAILABLE_CAPITAL", "max_quantity_per_order": 5000, "description": "Quantity calculation methods and limits"}, "order_management": {"order_type": "MARKET", "price_type": "MARKET", "validity": "DAY", "disclosed_quantity": 0, "trigger_price": null, "order_tag": "ZenStrato_Enhanced", "order_timeout": 30, "partial_fill_allowed": true, "iceberg_quantity": null, "description": "Order placement and execution settings"}, "position_management": {"max_positions": 5, "max_positions_per_symbol": 1, "position_sizing_method": "CAPITAL_PERCENTAGE", "position_size_percentage": 20, "allow_averaging": false, "averaging_levels": 3, "averaging_percentage": 50, "position_tracking": true, "auto_position_management": true, "description": "Position sizing and management rules"}, "risk_management": {"max_position_size": 0.2, "daily_loss_limit": 1000, "daily_profit_target": 2000, "max_drawdown_pct": 5, "stop_loss_pct": 2, "take_profit_pct": 4, "trailing_stop_loss": false, "trailing_stop_pct": 1, "max_consecutive_losses": 3, "risk_reward_ratio": 2.0, "emergency_stop_loss": true, "description": "Comprehensive risk management settings"}, "timeframe_config": {"primary_timeframe": "1m", "supported_timeframes": ["1m", "3m", "5m", "15m", "30m", "1h", "1d"], "data_range_hours": 2, "auto_adjust_indicators": true, "fetch_interval_override": null, "multi_timeframe_analysis": false, "higher_timeframe": "5m", "description": "Timeframe configuration and multi-timeframe analysis"}, "market_hours": {"market_open": "09:15", "market_close": "15:30", "pre_market_start": "09:00", "post_market_end": "16:00", "timezone": "Asia/Kolkata", "trading_days": [0, 1, 2, 3, 4], "holidays": ["2025-01-26", "2025-03-14", "2025-08-15", "2025-10-02"], "respect_market_hours": true, "description": "Market hours and trading session configuration"}, "product_specific": {"MIS": {"square_off_time": "14:50", "max_leverage": 5.0, "auto_square_off": true, "margin_required": 0.2, "holding_period": "SAME_DAY"}, "CNC": {"square_off_time": null, "max_leverage": 1.0, "auto_square_off": false, "margin_required": 1.0, "holding_period": "UNLIMITED"}, "CO": {"square_off_time": "15:15", "max_leverage": 3.0, "auto_square_off": true, "margin_required": 0.33, "stop_loss_mandatory": true, "holding_period": "SAME_DAY"}, "BO": {"square_off_time": "15:15", "max_leverage": 3.0, "auto_square_off": true, "margin_required": 0.33, "target_mandatory": true, "stop_loss_mandatory": true, "holding_period": "SAME_DAY"}, "description": "Product-specific configurations and rules"}, "enhanced_order_management": {"use_redis": false, "redis_host": "localhost", "redis_port": 6379, "redis_db": 0, "max_order_retries": 3, "retry_delay_seconds": 2, "retry_backoff_multiplier": 1.5, "max_concurrent_orders": 5, "order_monitoring": true, "order_status_check_interval": 5, "description": "Enhanced order management with retry logic"}, "indicators": {"rsi_period": 14, "rsi_oversold": 30, "rsi_overbought": 70, "macd_fast": 12, "macd_slow": 26, "macd_signal": 9, "ema_periods": [9, 21, 50], "sma_periods": [20, 50, 200], "bollinger_period": 20, "bollinger_std": 2, "atr_period": 14, "volume_sma_period": 20, "custom_indicators": {}, "description": "Technical indicator parameters and settings"}, "signal_management": {"signal_cooldown_seconds": 30, "min_signal_strength": 0.7, "signal_confirmation_required": false, "multiple_signal_sources": false, "signal_filtering": true, "noise_reduction": true, "signal_history_length": 100, "description": "Signal generation and filtering settings"}, "parallel_processing": {"enable_parallel": false, "max_concurrent_strategies": 2, "max_worker_threads": 3, "thread_pool_size": 5, "async_processing": true, "resource_sharing": true, "description": "Parallel processing and multi-threading configuration"}, "optimization": {"enable_caching": true, "cache_size": 500, "ltp_cache_duration": 5, "ohlcv_cache_duration": 10, "memory_cleanup_interval": 600, "max_api_calls_per_minute": 60, "performance_monitoring": true, "auto_optimization": true, "description": "Performance optimization and caching settings"}, "logging_and_monitoring": {"log_level": "INFO", "log_to_file": true, "log_file_path": "logs/strategy.log", "log_rotation": true, "max_log_size_mb": 100, "backup_count": 5, "trade_logging": true, "performance_logging": true, "error_notifications": true, "description": "Logging and monitoring configuration"}, "alerts_and_notifications": {"enable_alerts": true, "alert_methods": ["console", "file"], "email_alerts": false, "email_config": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "<EMAIL>", "password": "your_app_password", "to_email": "<EMAIL>"}, "webhook_alerts": false, "webhook_url": "https://your-webhook-url.com", "alert_levels": ["ERROR", "WARNING", "TRADE"], "description": "Alert and notification system configuration"}, "backtesting": {"enable_backtesting": false, "backtest_start_date": "2024-01-01", "backtest_end_date": "2024-12-31", "initial_capital": 100000, "commission_per_trade": 10, "slippage_pct": 0.1, "benchmark_symbol": "NIFTY50", "save_backtest_results": true, "description": "Backtesting configuration and parameters"}, "advanced_features": {"machine_learning": false, "ml_model_path": "models/strategy_model.pkl", "sentiment_analysis": false, "news_integration": false, "social_sentiment": false, "options_trading": false, "futures_trading": false, "crypto_trading": false, "description": "Advanced features and integrations"}, "custom_parameters": {"user_defined_param_1": "custom_value_1", "user_defined_param_2": 42, "user_defined_param_3": true, "strategy_specific_settings": {}, "experimental_features": {}, "description": "Custom user-defined parameters and experimental features"}}