# engine/utility/mode_manager.py

import json
import os
import logging
import asyncio
from typing import Optional, Callable
import requests
from datetime import datetime

class ZenStratoModeManager:
    """
    Manages ZenStrato trading mode with event-driven updates
    """
    
    def __init__(self):
        self.mode_file = ".zenstrato_mode.json"
        self.current_mode = None
        self.mode_change_callbacks = []
        self.logger = logging.getLogger("ZenStratoModeManager")
        
        # Load initial mode
        self._load_mode()
    
    def _load_mode(self):
        """Load mode from file or set default"""
        try:
            if os.path.exists(self.mode_file):
                with open(self.mode_file, 'r') as f:
                    data = json.load(f)
                    self.current_mode = data.get("mode", "live")
                    self.logger.info(f"Loaded mode from file: {self.current_mode}")
            else:
                # Default to live mode
                self.current_mode = "live"
                self._save_mode()
                self.logger.info("Initialized with default mode: live")
        except Exception as e:
            self.logger.error(f"Failed to load mode: {e}")
            self.current_mode = "live"
    
    def _save_mode(self):
        """Save current mode to file"""
        try:
            data = {
                "mode": self.current_mode,
                "last_updated": datetime.now().isoformat(),
                "source": "zenstrato"
            }
            with open(self.mode_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save mode: {e}")
    
    def get_mode(self) -> str:
        """Get current mode"""
        return self.current_mode
    
    def set_mode(self, mode: str, source: str = "manual"):
        """Set mode and notify callbacks"""
        if mode not in ["live", "api-analyser"]:
            raise ValueError(f"Invalid mode: {mode}. Must be 'live' or 'api-analyser'")
        
        old_mode = self.current_mode
        self.current_mode = mode
        self._save_mode()
        
        self.logger.info(f"Mode changed: {old_mode} → {mode} (source: {source})")
        
        # Notify all callbacks
        for callback in self.mode_change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    asyncio.create_task(callback(old_mode, mode))
                else:
                    callback(old_mode, mode)
            except Exception as e:
                self.logger.error(f"Error in mode change callback: {e}")
    
    def add_mode_change_callback(self, callback: Callable):
        """Add callback for mode changes"""
        self.mode_change_callbacks.append(callback)
        self.logger.debug(f"Added mode change callback: {callback.__name__}")
    
    def verify_mode_with_tradepilot(self, api_key: str, tradepilot_url: str = "http://127.0.0.1:5000") -> bool:
        """
        Verify current mode with TradePilot on startup
        Returns True if mode is synchronized, False if there's a mismatch
        """
        try:
            headers = {"X-API-KEY": api_key}
            response = requests.get(f"{tradepilot_url}/zenstrato/mode", headers=headers, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                tradepilot_mode = data.get("mode")
                
                if tradepilot_mode != self.current_mode:
                    self.logger.info(f"Mode mismatch detected. ZenStrato: {self.current_mode}, TradePilot: {tradepilot_mode}")
                    self.set_mode(tradepilot_mode, source="tradepilot_sync")
                    return False
                else:
                    self.logger.info(f"Mode synchronized: {self.current_mode}")
                    return True
            else:
                self.logger.warning(f"Failed to verify mode with TradePilot: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error verifying mode with TradePilot: {e}")
            return False
    
    def is_live_mode(self) -> bool:
        """Check if currently in live trading mode"""
        return self.current_mode == "live"
    
    def is_api_analyser_mode(self) -> bool:
        """Check if currently in API analyser mode"""
        return self.current_mode == "api-analyser"

# Global mode manager instance
mode_manager = ZenStratoModeManager()
