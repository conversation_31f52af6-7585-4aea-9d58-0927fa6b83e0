import pandas as pd
import numpy as np
from engine.core.strategy.istrategy import IStrategy
from engine.utility.resource_monitor import profile_function
import logging
import time

class Strategy(IStrategy):
    """
    Parallel trading test strategy for YESBANK
    
    This strategy demonstrates parallel order management:
    - Multiple simultaneous positions
    - Concurrent trade tracking
    - Advanced risk management
    """
    
    def __init__(self, config):
        super().__init__(config)
        self.config = config
        self.logger = logging.getLogger("ParallelTestYesBankStrategy")

        # Timeframe configuration
        self.timeframe = config.get("timeframe", "1m")
        self.symbol = config.get("symbol", "UNKNOWN")

        # Parallel trading parameters
        self.last_signal_time = 0
        self.signal_interval = 45  # New signal every 45 seconds
        self.position_duration = 60  # Hold positions for 60 seconds
        self.max_concurrent = config.get("legacy_params", {}).get("max_concurrent_trades", 3)
        
        # Track individual positions
        self.active_positions = {}  # {trade_id: {"entry_time": time, "entry_price": price}}
        self.trade_counter = 0
        
        # Risk management
        self.stop_loss_pct = config.get("sl_multiplier", 2.0)
        self.take_profit_pct = config.get("tp_multiplier", 4.0)

        self.logger.info(f"🔄 PARALLEL Strategy initialized for {self.symbol}")
        self.logger.info(f"🔄 Max concurrent trades: {self.max_concurrent}")
        self.logger.info(f"🔄 Signal interval: {self.signal_interval}s, Position duration: {self.position_duration}s")

    @profile_function("populate_indicators")
    def populate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Simple indicators for parallel trading test
        """
        if df.empty:
            return df
        
        df = df.copy()
        df['parallel_signal'] = 1  # Always ready for signals
        df['price_change'] = df['close'].pct_change()
        
        return df
    
    def entry_signal(self, df: pd.DataFrame) -> bool:
        """
        Parallel entry signal - can trigger multiple times for concurrent trades
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            current_time = time.time()
            current_price = df['close'].iloc[-1]
            
            # Clean up expired positions
            self._cleanup_expired_positions(current_time)
            
            # Log status every 30 seconds
            if int(current_time) % 30 == 0:
                active_count = len(self.active_positions)
                time_since_signal = current_time - self.last_signal_time
                self.logger.info(f"🔄 PARALLEL STATUS: Price=₹{current_price:.2f}, Active trades={active_count}/{self.max_concurrent}, Time since signal={time_since_signal:.0f}s")
            
            # Check if we can open a new position
            if len(self.active_positions) < self.max_concurrent:
                # Check if enough time has passed since last signal
                if (current_time - self.last_signal_time) >= self.signal_interval:
                    self.last_signal_time = current_time
                    self.trade_counter += 1
                    
                    # Create new position tracking
                    trade_id = f"parallel_trade_{self.trade_counter}"
                    self.active_positions[trade_id] = {
                        "entry_time": current_time,
                        "entry_price": current_price,
                        "trade_id": trade_id
                    }
                    
                    self.logger.info(f"🔄 PARALLEL BUY SIGNAL #{self.trade_counter}: Price=₹{current_price:.2f}, Active={len(self.active_positions)}/{self.max_concurrent}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in parallel entry_signal: {e}")
            return False
    
    def exit_signal(self, df: pd.DataFrame, current_price: float, entry_price: float = None, position_type: str = "BUY") -> bool:
        """
        Parallel exit signal - manages multiple positions independently
        """
        if df.empty:
            return False
        
        try:
            current_time = time.time()
            
            # Check if any positions should be closed
            positions_to_close = []
            
            for trade_id, position in self.active_positions.items():
                position_age = current_time - position["entry_time"]
                
                # Exit after position duration
                if position_age >= self.position_duration:
                    positions_to_close.append(trade_id)
                    self.logger.info(f"🔄 PARALLEL SELL SIGNAL {trade_id}: Price=₹{current_price:.2f}, Held for {position_age:.0f}s")
                
                # Also check stop loss / take profit
                elif entry_price and position["entry_price"]:
                    if position_type == "BUY":
                        stop_loss = position["entry_price"] * (1 - self.stop_loss_pct / 100)
                        take_profit = position["entry_price"] * (1 + self.take_profit_pct / 100)
                        
                        if current_price <= stop_loss:
                            positions_to_close.append(trade_id)
                            self.logger.info(f"🔄 PARALLEL STOP LOSS {trade_id}: {current_price} <= {stop_loss}")
                        
                        elif current_price >= take_profit:
                            positions_to_close.append(trade_id)
                            self.logger.info(f"🔄 PARALLEL TAKE PROFIT {trade_id}: {current_price} >= {take_profit}")
            
            # Close expired positions
            for trade_id in positions_to_close:
                if trade_id in self.active_positions:
                    del self.active_positions[trade_id]
            
            # Return True if any positions should be closed
            return len(positions_to_close) > 0
            
        except Exception as e:
            self.logger.error(f"Error in parallel exit_signal: {e}")
            return False
    
    def _cleanup_expired_positions(self, current_time):
        """
        Clean up positions that have exceeded maximum duration
        """
        expired_positions = []
        
        for trade_id, position in self.active_positions.items():
            position_age = current_time - position["entry_time"]
            if position_age > (self.position_duration + 30):  # 30s grace period
                expired_positions.append(trade_id)
        
        for trade_id in expired_positions:
            self.logger.warning(f"🔄 Cleaning up expired position: {trade_id}")
            del self.active_positions[trade_id]
    
    def get_strategy_info(self) -> dict:
        """
        Get parallel strategy information
        """
        return {
            "name": "PARALLEL_TEST_YESBANK",
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "indicators": ["parallel_signal", "price_change"],
            "parameters": {
                "timeframe": self.timeframe,
                "signal_interval": self.signal_interval,
                "position_duration": self.position_duration,
                "max_concurrent": self.max_concurrent,
                "stop_loss_pct": self.stop_loss_pct,
                "take_profit_pct": self.take_profit_pct
            },
            "parallel_status": {
                "trade_counter": self.trade_counter,
                "active_positions": len(self.active_positions),
                "max_concurrent": self.max_concurrent,
                "position_details": self.active_positions
            },
            "description": "Parallel trading strategy demonstrating concurrent position management"
        }
    
    def optimize_for_server(self):
        """
        Server-specific optimizations for parallel strategy
        """
        # Reset parallel state
        self.active_positions.clear()
        self.trade_counter = 0
        self.last_signal_time = 0
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info("🔄 Parallel strategy optimized for server resources")
