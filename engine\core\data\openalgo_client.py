import requests
import pandas as pd
from datetime import datetime, timedelta
import logging
from .base_client import BaseClient

class OpenAlgoClient(BaseClient):
    """
    Client that communicates with OpenAlgo (TradePilot) API endpoints
    instead of directly connecting to brokers
    """
    
    def __init__(self, config):
        self.config = config
        self.symbol = config["symbol"]
        self.exchange = config.get("exchange", "NSE")
        
        # OpenAlgo API configuration - handle both flat and nested config structures
        broker_config = config.get("broker_config", {})
        self.openalgo_host = (config.get("openalgo_host") or
                             broker_config.get("openalgo_host") or
                             "http://127.0.0.1:5000")
        self.openalgo_api_key = (config.get("openalgo_api_key") or
                                broker_config.get("openalgo_api_key"))

        if not self.openalgo_api_key:
            raise ValueError("OpenAlgo API key not found in config. Please set 'openalgo_api_key' or 'broker_config.openalgo_api_key'")
        
        # Request headers (OpenAlgo uses API key in request body, not headers)
        self.headers = {
            "Content-Type": "application/json"
        }
        
        # Setup logging
        self.logger = logging.getLogger(f"OpenAlgoClient-{self.symbol}")
        
    def _make_request(self, method, endpoint, data=None, params=None):
        """Make HTTP request to OpenAlgo API"""
        url = f"{self.openalgo_host}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, params=params, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"OpenAlgo API request failed: {e}")
            raise Exception(f"OpenAlgo API Error: {e}")
    
    async def get_ohlcv(self):
        """Get historical OHLCV data from OpenAlgo"""
        try:
            now = datetime.now()
            start_date = (now - timedelta(hours=2)).strftime("%Y-%m-%d")
            end_date = now.strftime("%Y-%m-%d")

            # OpenAlgo uses POST with data in body
            # Fix symbol format - remove exchange prefix if present
            symbol = self.symbol.split(":")[-1] if ":" in self.symbol else self.symbol

            request_data = {
                "apikey": self.openalgo_api_key,
                "symbol": symbol,
                "exchange": self.exchange,
                "interval": self.config.get("timeframe", "1m"),  # Use proper timeframe format
                "start_date": start_date,
                "end_date": end_date
            }

            data = self._make_request("POST", "/api/v1/history", data=request_data)

            # Convert to DataFrame - OpenAlgo returns data in nested format
            if data and data.get("status") == "success" and "data" in data:
                history_data = data["data"]
                if history_data:  # Check if data is not empty
                    df = pd.DataFrame(history_data)
                    return df

            self.logger.warning("No historical data received from OpenAlgo")
            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Failed to get OHLCV data: {e}")
            return pd.DataFrame()
    
    def get_current_ltp(self):
        """Get current Last Traded Price from OpenAlgo"""
        try:
            # OpenAlgo uses POST with data in body
            # Fix symbol format - remove exchange prefix if present
            symbol = self.symbol.split(":")[-1] if ":" in self.symbol else self.symbol

            request_data = {
                "apikey": self.openalgo_api_key,
                "symbol": symbol,
                "exchange": self.exchange
            }

            data = self._make_request("POST", "/api/v1/quotes", data=request_data)

            # OpenAlgo returns data in nested format: {"data": {"ltp": value}, "status": "success"}
            if data and data.get("status") == "success" and "data" in data:
                ltp_data = data["data"]
                if "ltp" in ltp_data:
                    return float(ltp_data["ltp"])

            self.logger.warning("No LTP data received from OpenAlgo")
            return 0.0

        except Exception as e:
            self.logger.error(f"Failed to get LTP: {e}")
            return 0.0
    
    async def get_ltp_event(self):
        """Get LTP for event-driven strategies (async version)"""
        return self.get_current_ltp()
    
    def place_order(self, side, quantity):
        """Place order through OpenAlgo API"""
        try:
            # Fix symbol format - remove exchange prefix if present
            symbol = self.symbol.split(":")[-1] if ":" in self.symbol else self.symbol

            # Get price type from config - handle both naming conventions
            price_type = (self.config.get("price_type") or
                         self.config.get("order_management", {}).get("price_type") or
                         "MARKET")

            order_data = {
                "apikey": self.openalgo_api_key,
                "strategy": "ZENSTRATO_ENGINE",
                "symbol": symbol,
                "action": side.upper(),  # BUY or SELL
                "exchange": self.exchange,
                "pricetype": price_type,  # OpenAlgo API expects 'pricetype'
                "product": self.config.get("product", "MIS"),
                "quantity": str(quantity)
            }

            # Add price for limit orders
            if price_type.upper() == "LIMIT":
                order_data["price"] = str(self.config.get("price", 0))
            
            response = self._make_request("POST", "/api/v1/placeorder", data=order_data)
            
            self.logger.info(f"Order placed: {side} {quantity} {self.symbol} - Response: {response}")
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to place order: {e}")
            return {"status": "error", "message": str(e)}
    
    def orderstatus(self, order_id):
        """Get order status from OpenAlgo"""
        try:
            request_data = {
                "apikey": self.openalgo_api_key,
                "strategy": "ZENSTRATO_ENGINE",
                "orderid": order_id
            }

            response = self._make_request("POST", "/api/v1/orderstatus", data=request_data)
            return response

        except Exception as e:
            self.logger.error(f"Failed to get order status: {e}")
            return {"status": "error", "message": str(e)}
    
    def funds(self):
        """Get account funds from OpenAlgo"""
        try:
            # OpenAlgo uses POST with data in body
            request_data = {
                "apikey": self.openalgo_api_key
            }

            response = self._make_request("POST", "/api/v1/funds", data=request_data)
            return response

        except Exception as e:
            self.logger.error(f"Failed to get funds: {e}")
            return {"equity_available": 0}
    
    def get_positions(self):
        """Get current positions from OpenAlgo"""
        try:
            request_data = {
                "apikey": self.openalgo_api_key
            }

            response = self._make_request("POST", "/api/v1/positionbook", data=request_data)
            return response

        except Exception as e:
            self.logger.error(f"Failed to get positions: {e}")
            return []

    def get_orderbook(self):
        """Get order book from OpenAlgo"""
        try:
            request_data = {
                "apikey": self.openalgo_api_key
            }

            response = self._make_request("POST", "/api/v1/orderbook", data=request_data)
            return response

        except Exception as e:
            self.logger.error(f"Failed to get orderbook: {e}")
            return []
    
    def health_check(self):
        """Check if OpenAlgo API is accessible"""
        try:
            # Test with funds endpoint using POST
            request_data = {
                "apikey": self.openalgo_api_key
            }

            response = self._make_request("POST", "/api/v1/funds", data=request_data)
            return {"status": "healthy", "openalgo_connected": True, "response": response}

        except Exception as e:
            return {"status": "unhealthy", "openalgo_connected": False, "error": str(e)}
