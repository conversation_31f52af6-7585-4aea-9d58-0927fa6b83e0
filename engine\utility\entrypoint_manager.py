# engine/utility/entrypoint_manager.py

import os
import asyncio
import logging
from datetime import datetime, time as dtime
from pytz import timezone
from engine.core.strategy.strategy_loader import load_strategy
from engine.core.strategy.strategy_runner import run_strategy, stop_event
from engine.core.data.client_factory import get_client
from engine.utility.alert_utils import send_alert
from engine.utility.setup_utils import setup_tradepilot_env
from engine.utility.tradepilot_utils import is_port_open, start_tradepilot, wait_for_tradepilot_ready, fetch_mode

CHECK_INTERVAL = 5  # seconds

def funds_ok(config):
    try:
        client = get_client(config)
        bal = float(client.funds().get("equity_available", 0))
        required = float(config.get("capital", 0))
        return bal >= required
    except Exception as e:
        logging.warning(f"⚠️ Fund check failed: {e}")
        return False

async def main_loop(strategy_name, tp_folder="TradePilot"):
    venv_path = setup_tradepilot_env(tp_folder)
    strategy, config = load_strategy(strategy_name)

    tp_proc = None
    if not is_port_open():
        tp_proc = start_tradepilot(venv_path)
        if not await wait_for_tradepilot_ready():
            logging.error("❌ TradePilot UI failed to start.")
            return
        logging.info("✅ TradePilot is running.")

    mode = None
    engine_task = None
    engine_running = False

    while True:
        new_mode = fetch_mode()
        if new_mode and new_mode != mode:
            mode = new_mode
            if engine_running:
                stop_event.set()
                await engine_task
                engine_running = False
                send_alert(config, strategy_name, "STOP", {
                    "reason": f"Mode switched → {mode}",
                    "engine_running": False,
                    "tradepilot_running": is_port_open()
                })

            if mode == "live":
                if funds_ok(config):
                    stop_event.clear()
                    engine_task = asyncio.create_task(run_strategy(strategy_name))
                    engine_running = True
                    send_alert(config, strategy_name, "START", {"mode": "LIVE", "message": "Engine started in LIVE mode"})
                else:
                    send_alert(config, strategy_name, "ERROR", {"message": "❌ Insufficient funds for LIVE mode"})

            elif mode == "api-analyser":
                stop_event.clear()
                engine_task = asyncio.create_task(run_strategy(strategy_name))
                engine_running = True
                send_alert(config, strategy_name, "START", {"mode": "API-ANALYSER", "message": "Engine started in ANALYSER mode"})

        now = datetime.now(timezone("Asia/Kolkata")).time()
        if config.get("product", "").upper() == "MIS" and now >= dtime(15, 0):
            stop_event.set()
            break

        await asyncio.sleep(CHECK_INTERVAL)

    if tp_proc:
        tp_proc.terminate()
        logging.info("🛑 TradePilot terminated.")
