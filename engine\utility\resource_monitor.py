import psutil
import time
import threading
import logging
from datetime import datetime
from typing import Dict, Any
import gc
import sys

class ResourceMonitor:
    """
    Lightweight resource monitoring for server optimization
    """
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = time.time()
        self.peak_memory = 0
        self.peak_cpu = 0
        self.api_call_count = 0
        self.data_fetch_count = 0
        self.indicator_calc_count = 0
        self.lock = threading.Lock()
        self.logger = logging.getLogger("ResourceMonitor")
        
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current resource usage"""
        try:
            # Memory usage
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # CPU usage
            cpu_percent = self.process.cpu_percent()
            
            # Update peaks
            with self.lock:
                self.peak_memory = max(self.peak_memory, memory_mb)
                self.peak_cpu = max(self.peak_cpu, cpu_percent)
            
            # System stats
            system_memory = psutil.virtual_memory()
            system_cpu = psutil.cpu_percent()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "uptime_seconds": time.time() - self.start_time,
                "memory": {
                    "current_mb": round(memory_mb, 2),
                    "peak_mb": round(self.peak_memory, 2),
                    "system_available_mb": round(system_memory.available / 1024 / 1024, 2),
                    "system_percent": system_memory.percent
                },
                "cpu": {
                    "current_percent": cpu_percent,
                    "peak_percent": self.peak_cpu,
                    "system_percent": system_cpu
                },
                "counters": {
                    "api_calls": self.api_call_count,
                    "data_fetches": self.data_fetch_count,
                    "indicator_calculations": self.indicator_calc_count
                },
                "gc_stats": {
                    "collections": gc.get_count(),
                    "objects": len(gc.get_objects())
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get resource stats: {e}")
            return {"error": str(e)}
    
    def increment_api_calls(self):
        """Track API call count"""
        with self.lock:
            self.api_call_count += 1
    
    def increment_data_fetches(self):
        """Track data fetch count"""
        with self.lock:
            self.data_fetch_count += 1
    
    def increment_indicator_calcs(self):
        """Track indicator calculation count"""
        with self.lock:
            self.indicator_calc_count += 1
    
    def force_gc(self):
        """Force garbage collection"""
        collected = gc.collect()
        self.logger.info(f"Garbage collection freed {collected} objects")
        return collected
    
    def get_memory_usage_by_type(self):
        """Get detailed memory usage breakdown"""
        try:
            import tracemalloc
            if tracemalloc.is_tracing():
                current, peak = tracemalloc.get_traced_memory()
                return {
                    "traced_current_mb": current / 1024 / 1024,
                    "traced_peak_mb": peak / 1024 / 1024
                }
        except ImportError:
            pass
        
        return {"traced_memory": "not_available"}
    
    def optimize_memory(self):
        """Perform memory optimization"""
        before_stats = self.get_current_stats()
        
        # Force garbage collection
        collected = self.force_gc()
        
        # Clear any cached data (if applicable)
        # This would be strategy-specific
        
        after_stats = self.get_current_stats()
        
        memory_saved = before_stats["memory"]["current_mb"] - after_stats["memory"]["current_mb"]
        
        self.logger.info(f"Memory optimization: {memory_saved:.2f}MB saved, {collected} objects collected")
        
        return {
            "memory_saved_mb": memory_saved,
            "objects_collected": collected,
            "before": before_stats,
            "after": after_stats
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for optimization analysis"""
        stats = self.get_current_stats()
        
        # Calculate efficiency metrics
        uptime_hours = stats["uptime_seconds"] / 3600
        api_calls_per_hour = self.api_call_count / max(uptime_hours, 0.01)
        memory_per_api_call = self.peak_memory / max(self.api_call_count, 1)
        
        return {
            "efficiency": {
                "api_calls_per_hour": round(api_calls_per_hour, 2),
                "memory_per_api_call_mb": round(memory_per_api_call, 4),
                "uptime_hours": round(uptime_hours, 2)
            },
            "resource_usage": stats,
            "optimization_recommendations": self._get_optimization_recommendations(stats)
        }
    
    def _get_optimization_recommendations(self, stats: Dict[str, Any]) -> list:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Memory recommendations
        if stats["memory"]["current_mb"] > 100:
            recommendations.append("HIGH_MEMORY_USAGE: Consider implementing data caching limits")
        
        if stats["memory"]["peak_mb"] > 200:
            recommendations.append("PEAK_MEMORY_HIGH: Implement periodic garbage collection")
        
        # CPU recommendations
        if stats["cpu"]["peak_percent"] > 80:
            recommendations.append("HIGH_CPU_USAGE: Optimize indicator calculations")
        
        # API call recommendations
        if self.api_call_count > 1000:
            recommendations.append("HIGH_API_CALLS: Implement request caching")
        
        # System recommendations
        if stats["memory"]["system_percent"] > 80:
            recommendations.append("SYSTEM_MEMORY_LOW: Reduce application memory footprint")
        
        if not recommendations:
            recommendations.append("PERFORMANCE_GOOD: No immediate optimizations needed")
        
        return recommendations

# Global instance
resource_monitor = ResourceMonitor()

def profile_function(func_name: str):
    """Decorator to profile function performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = resource_monitor.process.memory_info().rss / 1024 / 1024
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                end_memory = resource_monitor.process.memory_info().rss / 1024 / 1024
                
                execution_time = end_time - start_time
                memory_delta = end_memory - start_memory
                
                if execution_time > 1.0 or abs(memory_delta) > 5:  # Log only significant operations
                    resource_monitor.logger.info(
                        f"PROFILE {func_name}: {execution_time:.3f}s, "
                        f"Memory: {memory_delta:+.2f}MB"
                    )
        
        return wrapper
    return decorator
