import asyncio
import time
import gc
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional
import logging

from ..data.client_factory import get_client
from ..strategy.strategy_loader import load_strategy
from engine.utility.logger_utils import log_trade
from engine.utility.alert_utils import alert, send_alert
from engine.utility.strategy_health_monitor import health_monitor
from engine.utility.resource_monitor import resource_monitor, profile_function
from engine.core.data.optimized_data_handler import data_handler_pool

class OptimizedStrategyRunner:
    """
    Memory and CPU optimized strategy runner for server environments
    """
    
    def __init__(self, strategy_name: str):
        self.strategy_name = strategy_name
        self.strategy = None
        self.config = None
        self.client = None
        self.data_handler = None
        self.logger = logging.getLogger(f"OptimizedRunner-{strategy_name}")
        
        # Performance tracking
        self.start_time = time.time()
        self.last_cleanup_time = time.time()
        self.cleanup_interval = 600  # 10 minutes
        
        # Trade tracking
        self.active_trades = []
        self.trade_count = 0
        
        # Resource limits
        self.max_memory_mb = 150  # Maximum memory usage
        self.max_api_calls_per_minute = 60
        self.api_call_timestamps = []
        
    async def initialize(self):
        """
        Initialize strategy with optimizations
        """
        try:
            # Load strategy and config
            self.strategy, self.config = load_strategy(self.strategy_name)
            self.client = get_client(self.config)
            
            # Get optimized data handler
            symbol = self.config.get("symbol", "")
            self.data_handler = data_handler_pool.get_handler(symbol)
            
            # Register with health monitor
            health_monitor.register_strategy(self.strategy_name, self.config)
            health_monitor.start_strategy(self.strategy_name)
            
            # Apply server optimizations
            if hasattr(self.strategy, 'optimize_for_server'):
                self.strategy.optimize_for_server()
            
            self.logger.info(f"Optimized strategy runner initialized for {self.strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize strategy: {e}")
            return False
    
    @profile_function("strategy_loop")
    async def run_optimized_loop(self):
        """
        Main optimized strategy loop
        """
        loop_count = 0
        
        try:
            while True:
                loop_start_time = time.time()
                
                # Check resource usage and cleanup if needed
                await self._check_and_cleanup_resources()
                
                # Rate limiting for API calls
                if not self._check_rate_limit():
                    await asyncio.sleep(1)
                    continue
                
                # Send heartbeat
                health_monitor.heartbeat(self.strategy_name, {
                    "loop_count": loop_count,
                    "active_trades": len(self.active_trades),
                    "memory_usage": resource_monitor.get_current_stats()["memory"]["current_mb"]
                })
                
                # Get market data using optimized handler
                df = await self.data_handler.get_optimized_ohlcv(self.client)
                
                if df.empty:
                    self.logger.warning("No market data received")
                    await asyncio.sleep(10)
                    continue
                
                # Calculate indicators efficiently
                df_with_indicators = self.data_handler.calculate_indicators_optimized(
                    df, 
                    fast_period=self.config.get("fast_period", 5),
                    slow_period=self.config.get("slow_period", 20)
                )
                
                # For RSI/MACD strategy, use strategy's own indicator calculation
                if hasattr(self.strategy, 'populate_indicators'):
                    df_with_indicators = self.strategy.populate_indicators(df_with_indicators)
                
                # Check for entry signals
                if self.strategy.entry_signal(df_with_indicators):
                    await self._handle_entry_signal(df_with_indicators)
                
                # Check exit signals for active trades
                await self._check_exit_signals(df_with_indicators)
                
                # Performance monitoring
                loop_duration = time.time() - loop_start_time
                if loop_duration > 5.0:  # Log slow loops
                    self.logger.warning(f"Slow loop detected: {loop_duration:.2f}s")
                
                loop_count += 1
                
                # Adaptive sleep based on market activity
                sleep_duration = self._calculate_adaptive_sleep()
                await asyncio.sleep(sleep_duration)
                
        except Exception as e:
            self.logger.error(f"Strategy loop error: {e}")
            health_monitor.record_error(self.strategy_name, str(e))
            raise
    
    async def _handle_entry_signal(self, df: pd.DataFrame):
        """
        Handle entry signal with resource optimization
        """
        try:
            # Check if we can place more trades
            max_trades = self.config.get("max_concurrent_trades", 1)
            if len(self.active_trades) >= max_trades:
                return
            
            # Get current price efficiently
            current_price = self.data_handler.get_cached_ltp(self.client)
            if current_price <= 0:
                return
            
            # Calculate position size
            capital = self.config.get("capital", 10000)
            quantity = max(1, int(capital // current_price))
            
            # Place order
            response = self.client.place_order("BUY", quantity)
            
            if response.get("status") == "success":
                order_id = response.get("orderid")
                
                # Track trade
                trade_info = {
                    "order_id": order_id,
                    "entry_price": current_price,
                    "quantity": quantity,
                    "timestamp": datetime.now(),
                    "type": "BUY"
                }
                
                self.active_trades.append(trade_info)
                self.trade_count += 1
                
                # Log and alert
                self.logger.info(f"Entry order placed: {order_id} @ ₹{current_price}")
                alert(f"🟢 {self.strategy_name} BUY @ ₹{current_price}")
                
                # Record successful trade
                health_monitor.record_trade(self.strategy_name, {
                    "status": "success",
                    "action": "BUY",
                    "order_id": order_id,
                    "price": current_price,
                    "quantity": quantity
                })
                
            else:
                self.logger.error(f"Entry order failed: {response}")
                health_monitor.record_trade(self.strategy_name, {
                    "status": "failed",
                    "error": response.get("message", "Unknown error")
                })
                
        except Exception as e:
            self.logger.error(f"Error handling entry signal: {e}")
            health_monitor.record_error(self.strategy_name, str(e))
    
    async def _check_exit_signals(self, df: pd.DataFrame):
        """
        Check exit signals for active trades
        """
        if not self.active_trades:
            return
        
        try:
            current_price = self.data_handler.get_cached_ltp(self.client)
            
            for trade in self.active_trades[:]:  # Copy list to avoid modification during iteration
                # Check exit signal
                should_exit = self.strategy.exit_signal(
                    df, current_price, trade["entry_price"], trade["type"]
                )
                
                if should_exit:
                    # Place exit order
                    response = self.client.place_order("SELL", trade["quantity"])
                    
                    if response.get("status") == "success":
                        # Remove from active trades
                        self.active_trades.remove(trade)
                        
                        # Calculate P&L
                        pnl = (current_price - trade["entry_price"]) * trade["quantity"]
                        
                        self.logger.info(f"Exit order placed: {response.get('orderid')} @ ₹{current_price}, P&L: ₹{pnl:.2f}")
                        alert(f"🔴 {self.strategy_name} SELL @ ₹{current_price}, P&L: ₹{pnl:.2f}")
                        
                        # Record trade completion
                        health_monitor.record_trade(self.strategy_name, {
                            "status": "success",
                            "action": "SELL",
                            "order_id": response.get("orderid"),
                            "price": current_price,
                            "quantity": trade["quantity"],
                            "pnl": pnl
                        })
                        
        except Exception as e:
            self.logger.error(f"Error checking exit signals: {e}")
    
    async def _check_and_cleanup_resources(self):
        """
        Check resource usage and cleanup if needed
        """
        current_time = time.time()
        
        # Periodic cleanup
        if (current_time - self.last_cleanup_time) > self.cleanup_interval:
            await self._perform_cleanup()
            self.last_cleanup_time = current_time
        
        # Check memory usage
        stats = resource_monitor.get_current_stats()
        current_memory = stats["memory"]["current_mb"]
        
        if current_memory > self.max_memory_mb:
            self.logger.warning(f"High memory usage: {current_memory}MB, performing cleanup")
            await self._perform_cleanup()
    
    async def _perform_cleanup(self):
        """
        Perform resource cleanup
        """
        try:
            # Cleanup data handler cache
            self.data_handler.cleanup_cache()
            
            # Cleanup data handler pool
            data_handler_pool.cleanup_all()
            
            # Force garbage collection
            collected = resource_monitor.force_gc()
            
            # Clear old API call timestamps
            current_time = time.time()
            self.api_call_timestamps = [
                ts for ts in self.api_call_timestamps 
                if (current_time - ts) < 60
            ]
            
            self.logger.info(f"Cleanup completed: {collected} objects collected")
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")
    
    def _check_rate_limit(self) -> bool:
        """
        Check API rate limiting
        """
        current_time = time.time()
        
        # Remove old timestamps
        self.api_call_timestamps = [
            ts for ts in self.api_call_timestamps 
            if (current_time - ts) < 60
        ]
        
        # Check if we're under the limit
        if len(self.api_call_timestamps) >= self.max_api_calls_per_minute:
            return False
        
        # Add current timestamp
        self.api_call_timestamps.append(current_time)
        return True
    
    def _calculate_adaptive_sleep(self) -> float:
        """
        Calculate adaptive sleep duration based on market conditions
        """
        # Base sleep duration
        base_sleep = 10.0
        
        # Reduce sleep during active trading
        if self.active_trades:
            base_sleep = 5.0
        
        # Increase sleep during low activity periods
        current_hour = datetime.now().hour
        if current_hour < 9 or current_hour > 15:  # Outside market hours
            base_sleep = 30.0
        
        return base_sleep
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics
        """
        uptime = time.time() - self.start_time
        
        return {
            "strategy_name": self.strategy_name,
            "uptime_seconds": uptime,
            "total_trades": self.trade_count,
            "active_trades": len(self.active_trades),
            "resource_stats": resource_monitor.get_current_stats(),
            "data_handler_stats": self.data_handler.get_memory_usage() if self.data_handler else None
        }

# Factory function for optimized runner
async def run_optimized_strategy(strategy_name: str):
    """
    Run strategy with optimizations
    """
    runner = OptimizedStrategyRunner(strategy_name)
    
    if await runner.initialize():
        await runner.run_optimized_loop()
    else:
        raise Exception(f"Failed to initialize optimized strategy: {strategy_name}")
