# engine/utility/tradepilot_utils.py

import os
import socket
import subprocess
import requests
import logging
from dotenv import load_dotenv

load_dotenv(dotenv_path=os.path.join("TradePilot", ".env"))

TP_HOST = os.getenv("TP_HOST", "127.0.0.1")
TP_PORT = int(os.getenv("TP_PORT", 5000))
TP_PROXY_URL = os.getenv("TP_PROXY_URL", "http://127.0.0.1:5400/get-mode")
TP_API_KEY_FILE = os.getenv("TP_API_KEY_FILE", ".zen-api-key")

def is_port_open(host=TP_HOST, port=TP_PORT):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        return sock.connect_ex((host, port)) == 0

def start_tradepilot(venv_path, folder="TradePilot"):
    python_exec = os.path.join(
        venv_path, "Scripts", "python.exe" if os.name == "nt" else "bin/python"
    )

    try:
        proc = subprocess.Popen(
            [python_exec, "app.py"],
            cwd=folder,
            env=os.environ,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == "nt" else 0
        )
        return proc
    except FileNotFoundError as e:
        logging.error(f"❌ Failed to start TradePilot app.py: {e}")
        return None

async def wait_for_tradepilot_ready(timeout=20):
    import asyncio
    for _ in range(timeout):
        if is_port_open():
            return True
        await asyncio.sleep(1)
    return False

def fetch_mode():
    try:
        api_key_path = os.path.join("TradePilot", ".zen-api-key")
        with open(api_key_path) as f:
            key = f.read().strip()

        r = requests.get("http://127.0.0.1:5400/get-mode", headers={"X-API-KEY": key}, timeout=2)
        data = r.json()
        if isinstance(data.get("mode"), int):
            return "api-analyser" if data["mode"] == 1 else "live"
        return None
    except Exception as e:
        logging.warning(f"⚠️ Failed to fetch mode from zen_proxy: {e}")
        return None
