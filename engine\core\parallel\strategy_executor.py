import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Set
from concurrent.futures import ThreadPoolExecutor
import threading
from dataclasses import dataclass

from ..strategy.strategy_loader import load_strategy
from ..data.client_factory import get_client
from ..order_management.enhanced_order_manager import EnhancedOrderManager
from engine.utility.strategy_health_monitor import health_monitor
from engine.utility.resource_monitor import resource_monitor
from engine.core.data.optimized_data_handler import data_handler_pool

@dataclass
class StrategyTask:
    strategy_name: str
    config: Dict[str, Any]
    priority: int = 1
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

class ParallelStrategyExecutor:
    """
    Parallel strategy execution engine with queue management and resource control
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("ParallelStrategyExecutor")
        
        # Parallel processing configuration
        self.max_concurrent_strategies = config.get("max_concurrent_strategies", 3)
        self.max_worker_threads = config.get("max_worker_threads", 5)
        self.strategy_semaphore = asyncio.Semaphore(self.max_concurrent_strategies)
        
        # Task management
        self.strategy_queue = asyncio.PriorityQueue()
        self.running_strategies: Set[str] = set()
        self.strategy_tasks: Dict[str, asyncio.Task] = {}
        
        # Order management
        self.order_manager = EnhancedOrderManager(config)
        
        # Thread pool for CPU-intensive tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_worker_threads)
        
        # Performance tracking
        self.execution_stats = {
            "strategies_executed": 0,
            "total_execution_time": 0,
            "average_execution_time": 0,
            "concurrent_peak": 0,
            "queue_peak": 0
        }
        
        # Shutdown event
        self.shutdown_event = asyncio.Event()
        
    async def start(self):
        """
        Start the parallel execution engine
        """
        self.logger.info("Starting parallel strategy executor")
        
        # Start background tasks
        asyncio.create_task(self._queue_processor())
        asyncio.create_task(self._health_monitor())
        asyncio.create_task(self._resource_monitor())
        
        self.logger.info(f"Parallel executor started with {self.max_concurrent_strategies} concurrent strategies")
    
    async def add_strategy(self, strategy_name: str, priority: int = 1):
        """
        Add strategy to execution queue
        """
        try:
            # Load strategy config
            _, config = load_strategy(strategy_name)
            
            # Create task
            task = StrategyTask(
                strategy_name=strategy_name,
                config=config,
                priority=priority
            )
            
            # Add to queue (lower priority number = higher priority)
            await self.strategy_queue.put((priority, time.time(), task))
            
            # Update queue peak
            queue_size = self.strategy_queue.qsize()
            self.execution_stats["queue_peak"] = max(self.execution_stats["queue_peak"], queue_size)
            
            self.logger.info(f"Strategy {strategy_name} added to queue (priority: {priority}, queue size: {queue_size})")
            
        except Exception as e:
            self.logger.error(f"Failed to add strategy {strategy_name}: {e}")
    
    async def _queue_processor(self):
        """
        Process strategy queue
        """
        while not self.shutdown_event.is_set():
            try:
                # Wait for strategy task
                priority, timestamp, task = await asyncio.wait_for(
                    self.strategy_queue.get(), timeout=1.0
                )
                
                # Check if strategy is already running
                if task.strategy_name in self.running_strategies:
                    self.logger.warning(f"Strategy {task.strategy_name} already running, skipping")
                    continue
                
                # Wait for available slot
                await self.strategy_semaphore.acquire()
                
                # Execute strategy
                strategy_task = asyncio.create_task(
                    self._execute_strategy(task)
                )
                
                # Track task
                self.strategy_tasks[task.strategy_name] = strategy_task
                self.running_strategies.add(task.strategy_name)
                
                # Update concurrent peak
                concurrent_count = len(self.running_strategies)
                self.execution_stats["concurrent_peak"] = max(
                    self.execution_stats["concurrent_peak"], concurrent_count
                )
                
                # Cleanup when done
                strategy_task.add_done_callback(
                    lambda t, name=task.strategy_name: self._cleanup_strategy(name)
                )
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Queue processor error: {e}")
                await asyncio.sleep(1)
    
    async def _execute_strategy(self, task: StrategyTask):
        """
        Execute a single strategy
        """
        strategy_name = task.strategy_name
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting strategy execution: {strategy_name}")
            
            # Load strategy and client
            strategy, config = load_strategy(strategy_name)
            client = get_client(config)
            
            # Get optimized data handler
            symbol = config.get("symbol", "")
            data_handler = data_handler_pool.get_handler(symbol)
            
            # Register with health monitor
            health_monitor.register_strategy(strategy_name, config)
            health_monitor.start_strategy(strategy_name)
            
            # Strategy execution loop
            loop_count = 0
            last_signal_time = 0
            signal_cooldown = config.get("signal_cooldown_seconds", 30)
            
            while not self.shutdown_event.is_set():
                loop_start = time.time()
                
                try:
                    # Send heartbeat
                    health_monitor.heartbeat(strategy_name, {
                        "loop_count": loop_count,
                        "execution_time": time.time() - start_time,
                        "last_loop_duration": time.time() - loop_start
                    })
                    
                    # Get market data
                    df = await data_handler.get_optimized_ohlcv(client)
                    
                    if df.empty:
                        await asyncio.sleep(10)
                        continue
                    
                    # Calculate indicators (CPU-intensive, use thread pool)
                    df_with_indicators = await asyncio.get_event_loop().run_in_executor(
                        self.thread_pool,
                        self._calculate_indicators_threaded,
                        strategy, df
                    )
                    
                    # Check entry signals
                    current_time = time.time()
                    if (current_time - last_signal_time) > signal_cooldown:
                        if strategy.entry_signal(df_with_indicators):
                            await self._handle_entry_signal(
                                strategy_name, client, data_handler, config
                            )
                            last_signal_time = current_time
                    
                    # Check exit signals for active trades
                    await self._check_exit_signals(strategy_name, strategy, client, df_with_indicators)
                    
                    loop_count += 1
                    
                    # Adaptive sleep
                    sleep_duration = self._calculate_sleep_duration(config, loop_count)
                    await asyncio.sleep(sleep_duration)
                    
                except Exception as e:
                    self.logger.error(f"Strategy {strategy_name} loop error: {e}")
                    health_monitor.record_error(strategy_name, str(e))
                    await asyncio.sleep(5)
            
        except Exception as e:
            self.logger.error(f"Strategy {strategy_name} execution error: {e}")
            health_monitor.record_error(strategy_name, str(e))
        
        finally:
            # Update execution stats
            execution_time = time.time() - start_time
            self.execution_stats["strategies_executed"] += 1
            self.execution_stats["total_execution_time"] += execution_time
            self.execution_stats["average_execution_time"] = (
                self.execution_stats["total_execution_time"] / 
                self.execution_stats["strategies_executed"]
            )
            
            # Stop strategy in health monitor
            health_monitor.stop_strategy(strategy_name, "completed")
            
            self.logger.info(f"Strategy {strategy_name} execution completed in {execution_time:.2f}s")
    
    def _calculate_indicators_threaded(self, strategy, df):
        """
        Calculate indicators in thread pool (CPU-intensive)
        """
        if hasattr(strategy, 'populate_indicators'):
            return strategy.populate_indicators(df)
        return df
    
    async def _handle_entry_signal(self, strategy_name: str, client, data_handler, config: Dict[str, Any]):
        """
        Handle entry signal with enhanced order management
        """
        try:
            # Get current price
            current_price = data_handler.get_cached_ltp(client)
            if current_price <= 0:
                return
            
            # Calculate position size
            capital = config.get("capital", 10000)
            quantity = max(1, int(capital // current_price))
            
            # Place order using enhanced order manager
            result = await self.order_manager.place_order_with_retry(
                client=client,
                order_type="BUY",
                quantity=quantity,
                strategy_name=strategy_name,
                symbol=config.get("symbol", ""),
                price=current_price
            )
            
            if result["status"] == "success":
                self.logger.info(f"Entry order placed for {strategy_name}: {result['tradepilot_order_id']}")
                health_monitor.record_trade(strategy_name, {
                    "status": "success",
                    "action": "BUY",
                    "order_id": result["order_id"],
                    "tradepilot_order_id": result["tradepilot_order_id"],
                    "price": current_price,
                    "quantity": quantity
                })
            else:
                self.logger.error(f"Entry order failed for {strategy_name}: {result['error']}")
                health_monitor.record_trade(strategy_name, {
                    "status": "failed",
                    "action": "BUY",
                    "error": result["error"],
                    "retry_count": result.get("retry_count", 0)
                })
                
        except Exception as e:
            self.logger.error(f"Error handling entry signal for {strategy_name}: {e}")
            health_monitor.record_error(strategy_name, str(e))
    
    async def _check_exit_signals(self, strategy_name: str, strategy, client, df):
        """
        Check exit signals for active trades
        """
        try:
            # Get active orders for this strategy
            active_orders = await self.order_manager.get_strategy_orders(strategy_name)
            filled_orders = [o for o in active_orders if o["status"] == "filled"]
            
            if not filled_orders:
                return
            
            current_price = client.get_current_ltp()
            
            for order in filled_orders:
                # Check exit signal
                entry_price = order.get("price", 0)
                should_exit = strategy.exit_signal(df, current_price, entry_price, "BUY")
                
                if should_exit:
                    # Place exit order
                    result = await self.order_manager.place_order_with_retry(
                        client=client,
                        order_type="SELL",
                        quantity=order["quantity"],
                        strategy_name=strategy_name,
                        symbol=order["symbol"],
                        price=current_price
                    )
                    
                    if result["status"] == "success":
                        pnl = (current_price - entry_price) * order["quantity"]
                        self.logger.info(f"Exit order placed for {strategy_name}: P&L ₹{pnl:.2f}")
                        
                        health_monitor.record_trade(strategy_name, {
                            "status": "success",
                            "action": "SELL",
                            "order_id": result["order_id"],
                            "price": current_price,
                            "quantity": order["quantity"],
                            "pnl": pnl
                        })
                        
        except Exception as e:
            self.logger.error(f"Error checking exit signals for {strategy_name}: {e}")
    
    def _calculate_sleep_duration(self, config: Dict[str, Any], loop_count: int) -> float:
        """
        Calculate adaptive sleep duration
        """
        base_sleep = config.get("loop_interval_seconds", 10)
        
        # Reduce frequency during off-market hours
        current_hour = datetime.now().hour
        if current_hour < 9 or current_hour > 15:
            return base_sleep * 3
        
        # Increase frequency during high volatility periods
        if loop_count % 100 == 0:  # Every 100 loops, check for high activity
            return base_sleep * 0.5
        
        return base_sleep
    
    def _cleanup_strategy(self, strategy_name: str):
        """
        Cleanup after strategy completion
        """
        self.running_strategies.discard(strategy_name)
        self.strategy_tasks.pop(strategy_name, None)
        self.strategy_semaphore.release()
        
        self.logger.info(f"Strategy {strategy_name} cleaned up")
    
    async def _health_monitor(self):
        """
        Monitor system health
        """
        while not self.shutdown_event.is_set():
            try:
                stats = resource_monitor.get_current_stats()
                
                # Log resource usage periodically
                if stats["memory"]["current_mb"] > 200:
                    self.logger.warning(f"High memory usage: {stats['memory']['current_mb']:.2f}MB")
                
                # Auto-cleanup if needed
                if len(self.running_strategies) > self.max_concurrent_strategies * 0.8:
                    resource_monitor.optimize_memory()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(60)
    
    async def _resource_monitor(self):
        """
        Monitor and optimize resources
        """
        while not self.shutdown_event.is_set():
            try:
                # Cleanup data handler pool
                data_handler_pool.cleanup_all()
                
                # Force garbage collection periodically
                if len(self.running_strategies) > 2:
                    resource_monitor.force_gc()
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Resource monitor error: {e}")
                await asyncio.sleep(300)
    
    async def stop(self):
        """
        Stop the executor gracefully
        """
        self.logger.info("Stopping parallel strategy executor")
        
        # Set shutdown event
        self.shutdown_event.set()
        
        # Cancel all running tasks
        for task in self.strategy_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self.strategy_tasks:
            await asyncio.gather(*self.strategy_tasks.values(), return_exceptions=True)
        
        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("Parallel strategy executor stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get executor status
        """
        return {
            "running_strategies": list(self.running_strategies),
            "queue_size": self.strategy_queue.qsize(),
            "max_concurrent": self.max_concurrent_strategies,
            "execution_stats": self.execution_stats,
            "order_stats": self.order_manager.get_order_statistics()
        }
