import os
import subprocess
import shutil
import sys
import json
from pathlib import Path

TP_REPO = "https://github.com/bvsaisujith/TradePilot.git"
SETUP_STATUS_FILE = ".zenstrato_setup_status.json"

def get_setup_status():
    """Get the current setup status"""
    if not os.path.exists(SETUP_STATUS_FILE):
        return {}

    try:
        with open(SETUP_STATUS_FILE, 'r') as f:
            return json.load(f)
    except:
        return {}

def save_setup_status(status):
    """Save the setup status"""
    try:
        with open(SETUP_STATUS_FILE, 'w') as f:
            json.dump(status, f, indent=2)
    except Exception as e:
        print(f"⚠️ Warning: Could not save setup status: {e}")

def is_setup_complete():
    """Check if the initial setup is complete"""
    status = get_setup_status()
    required_components = [
        'tradepilot_cloned',
        'dependencies_installed',
        'api_key_generated',
        'modules_injected',
        'app_patched'
    ]

    return all(status.get(component, False) for component in required_components)

def verify_setup_integrity():
    """Verify that all setup components are still intact"""
    issues = []

    # Check if TradePilot directory exists
    if not os.path.exists("TradePilot"):
        issues.append("TradePilot directory missing")

    # Check if .env file exists
    if not os.path.exists("TradePilot/.env"):
        issues.append("TradePilot/.env file missing")

    # Check if ZenStrato modules are injected
    required_files = [
        "TradePilot/zenstrato_api.py",
        "TradePilot/utils/zenstrato_notifier.py",
        "TradePilot/blueprints/settings.py"
    ]

    for file_path in required_files:
        if not os.path.exists(file_path):
            issues.append(f"Required file missing: {file_path}")

    return issues

def is_tradepilot_running():
    """Check if TradePilot is running on port 5000"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('127.0.0.1', 5000))
            return result == 0
    except:
        return False

ZEN_INJECT_MAP = {
    "zenstrato_api.py": "",
    "zenstrato_notifier.py": "utils/",
    "settings.py": "blueprints/settings.py",
}


def clone_tradepilot_repo(tp_folder):
    if not os.path.exists(tp_folder):
        print(f"📥 Cloning TradePilot into {tp_folder} ...")
        try:
            subprocess.run(["git", "clone", TP_REPO, tp_folder], check=True)
            print("✅ TradePilot cloned successfully.")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to clone TradePilot repo: {e}")
            exit(1)

def dependencies_installed(pip_path, req_file):
    try:
        result = subprocess.run(
            [pip_path, "install", "-r", req_file, "--dry-run"],
            capture_output=True,
            text=True
        )
        return "Would install" not in result.stdout
    except Exception as e:
        print(f"⚠️ Could not verify dependencies from {req_file}: {e}")
        return False

def install_dependencies(pip_path, requirements):
    for req in requirements:
        if os.path.exists(req):
            if dependencies_installed(pip_path, req):
                print(f"✅ Dependencies already satisfied in {req}")
            else:
                print(f"📦 Installing dependencies from {req}...")
                try:
                    subprocess.run([pip_path, "install", "-r", req], check=True)
                    print(f"✅ Installed dependencies from {req}")
                except subprocess.CalledProcessError as e:
                    print(f"❌ Failed to install dependencies from {req}: {e}")
        else:
            print(f"⚠️ File not found: {req}")

def insert_blueprint_after_master(lines):
    for i, line in enumerate(lines):
        if "app.register_blueprint(master_contract_status_bp)" in line:
            indent = line[:len(line) - len(line.lstrip())]
            zen_register = f"{indent}app.register_blueprint(zen_bp)\n"
            if zen_register.strip() not in "".join(lines):
                lines.insert(i + 1, zen_register)
                print("✅ Inserted zen_bp registration after master_contract_status_bp")
            break
    return lines

def patch_app_py(app_path):
    with open(app_path, "r") as f:
        lines = f.readlines()

    if not any("from zenstrato_api import zen_bp" in l for l in lines):
        for i, line in enumerate(lines):
            if "from flask" in line:
                lines.insert(i + 1, "from zenstrato_api import zen_bp\n")
                print("✅ Inserted zen_bp import")
                break

    lines = insert_blueprint_after_master(lines)

    with open(app_path, "w") as f:
        f.writelines(lines)

    print("✅ Patched app.py to register ZenStrato blueprint")

def inject_zen_modules(base_folder="TradePilot"):
    print("\n🔁 Injecting ZenStrato modules into TradePilot...")
    inject_root = os.path.join(os.getcwd(), "zen_inject")

    for source, target_rel in ZEN_INJECT_MAP.items():
        source_path = os.path.join(inject_root, source)

        if not os.path.exists(source_path):
            print(f"⚠️ Skipped missing file: {source_path}")
            continue

        if target_rel.endswith(".py"):
            target_path = os.path.join(base_folder, target_rel)
        else:
            target_path = os.path.join(base_folder, target_rel, source)

        os.makedirs(os.path.dirname(target_path), exist_ok=True)
        shutil.copy2(source_path, target_path)
        print(f"✅ Injected {source} → {target_path}")

def smart_setup_tradepilot_env(tp_folder="TradePilot", force_setup=False):
    """
    Smart setup that only runs full setup when needed.
    For strategy execution, this should be used instead of setup_tradepilot_env.
    """
    # Check if setup is already complete and not forced
    if not force_setup and is_setup_complete():
        print("✅ Setup already complete, verifying integrity...")
        issues = verify_setup_integrity()

        if not issues:
            print("✅ All components verified, skipping setup")
            # Just ensure TradePilot is running
            if not is_tradepilot_running():
                print("🚀 Starting TradePilot...")
                start_tradepilot_only(tp_folder)
            else:
                print("✅ TradePilot is already running")
            return None
        else:
            print("⚠️ Setup integrity issues found:")
            for issue in issues:
                print(f"   - {issue}")
            print("🔧 Running repair setup...")
            # Fall through to full setup

    # Run full setup
    return setup_tradepilot_env(tp_folder, include_nginx=False)

def start_tradepilot_only(tp_folder="TradePilot"):
    """Start TradePilot without running full setup"""
    venv_path = os.path.join(tp_folder, "venv")
    python_venv = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "python" + (".exe" if os.name == "nt" else ""))

    print("🚀 Launching TradePilot app.py ...")
    try:
        subprocess.Popen(
            [python_venv, "app.py"],
            cwd=tp_folder,
            env=os.environ,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == "nt" else 0
        )
        print("✅ TradePilot UI is running.")
    except Exception as e:
        print(f"❌ Failed to start app.py: {e}")

def setup_tradepilot_env(tp_folder="TradePilot", include_nginx=False):
    print("\n🔧 Running setup_tradepilot_env")
    status = get_setup_status()

    clone_tradepilot_repo(tp_folder)
    status['tradepilot_cloned'] = True

    venv_path = os.path.join(tp_folder, "venv")
    env_file = os.path.join(tp_folder, ".env")
    sample_env = os.path.join(tp_folder, ".sample.env")
    app_py_path = os.path.join(tp_folder, "app.py")

    python_venv = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "python" + (".exe" if os.name == "nt" else ""))
    pip_venv = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "pip" + (".exe" if os.name == "nt" else ""))

    if not os.path.exists(venv_path):
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        print("✅ Virtual environment created.")

    reqs = [os.path.join(tp_folder, "requirements.txt")]
    if include_nginx:
        reqs.append(os.path.join(tp_folder, "requirement-nginx.txt"))
    install_dependencies(pip_venv, reqs)
    status['dependencies_installed'] = True

    if not os.path.exists(env_file) and os.path.exists(sample_env):
        print("\n⚙️ Setting up .env from .sample.env")
        broker = input(" Broker (e.g. angelone): ").strip()
        api_key = input(" API Key: ").strip()
        api_secret = input(" API Secret: ").strip()
        callback = f"http://127.0.0.1:5000/{broker}/callback"

        shutil.copy(sample_env, env_file)

        with open(env_file, "r+") as f:
            lines = f.readlines()
            f.seek(0)
            for line in lines:
                if "BROKER_API_KEY" in line:
                    f.write(f"BROKER_API_KEY = '{api_key}'\n")
                elif "BROKER_API_SECRET" in line:
                    f.write(f"BROKER_API_SECRET = '{api_secret}'\n")
                elif "REDIRECT_URL" in line:
                    f.write(f"REDIRECT_URL = '{callback}'\n")
                else:
                    f.write(line)
            f.truncate()
        print("✅ .env created.")

    # Generate ZenStrato API key only if it doesn't exist
    from engine.utility.zen_api_manager import get_existing_key, write_api_key, generate_api_key
    existing_key = get_existing_key()
    if not existing_key:
        write_api_key(generate_api_key())
        print("✅ Generated new ZenStrato API key")
        status['api_key_generated'] = True
    else:
        print(f"✅ Using existing ZenStrato API key: {existing_key}")
        status['api_key_generated'] = True

    # Inject ZenStrato modules
    inject_zen_modules(tp_folder)
    patch_app_py(app_py_path)
    status['modules_injected'] = True
    status['app_patched'] = True

    # Save setup status
    save_setup_status(status)
    print("✅ Setup completed and status saved")

    print("🚀 Launching TradePilot app.py ...")
    try:
        subprocess.Popen(
            [python_venv, "app.py"],
            cwd=tp_folder,
            env=os.environ,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == "nt" else 0
        )
        print("✅ TradePilot UI is running.")
    except Exception as e:
        print(f"❌ Failed to start app.py: {e}")

    return venv_path
