import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json
import os

class StrategyHealthMonitor:
    """
    Monitors strategy execution health and provides status information
    """
    
    def __init__(self):
        self.strategies = {}  # strategy_name -> health_data
        self.lock = threading.Lock()
        self.logger = logging.getLogger("StrategyHealthMonitor")
        
    def register_strategy(self, strategy_name: str, config: Dict[str, Any]):
        """Register a strategy for monitoring"""
        with self.lock:
            self.strategies[strategy_name] = {
                "name": strategy_name,
                "config": config,
                "status": "registered",
                "start_time": None,
                "last_heartbeat": None,
                "last_trade_time": None,
                "total_trades": 0,
                "successful_trades": 0,
                "failed_trades": 0,
                "current_position": None,
                "last_error": None,
                "openalgo_connection": "unknown",
                "health_score": 100
            }
        self.logger.info(f"Strategy '{strategy_name}' registered for monitoring")
    
    def start_strategy(self, strategy_name: str):
        """Mark strategy as started"""
        with self.lock:
            if strategy_name in self.strategies:
                self.strategies[strategy_name].update({
                    "status": "running",
                    "start_time": datetime.now(),
                    "last_heartbeat": datetime.now()
                })
        self.logger.info(f"Strategy '{strategy_name}' started")
    
    def stop_strategy(self, strategy_name: str, reason: str = "manual"):
        """Mark strategy as stopped"""
        with self.lock:
            if strategy_name in self.strategies:
                self.strategies[strategy_name].update({
                    "status": "stopped",
                    "stop_reason": reason,
                    "stop_time": datetime.now()
                })
        self.logger.info(f"Strategy '{strategy_name}' stopped: {reason}")
    
    def heartbeat(self, strategy_name: str, data: Optional[Dict[str, Any]] = None):
        """Update strategy heartbeat"""
        with self.lock:
            if strategy_name in self.strategies:
                self.strategies[strategy_name]["last_heartbeat"] = datetime.now()
                if data:
                    self.strategies[strategy_name].update(data)
    
    def record_trade(self, strategy_name: str, trade_data: Dict[str, Any]):
        """Record a trade execution"""
        with self.lock:
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                strategy["last_trade_time"] = datetime.now()
                strategy["total_trades"] += 1
                
                if trade_data.get("status") == "success":
                    strategy["successful_trades"] += 1
                else:
                    strategy["failed_trades"] += 1
                    strategy["last_error"] = trade_data.get("error")
                
                # Update health score
                success_rate = strategy["successful_trades"] / strategy["total_trades"]
                strategy["health_score"] = int(success_rate * 100)
    
    def record_error(self, strategy_name: str, error: str):
        """Record an error for the strategy"""
        with self.lock:
            if strategy_name in self.strategies:
                self.strategies[strategy_name]["last_error"] = error
                self.strategies[strategy_name]["health_score"] = max(0, 
                    self.strategies[strategy_name]["health_score"] - 10)
        self.logger.error(f"Strategy '{strategy_name}' error: {error}")
    
    def update_openalgo_status(self, strategy_name: str, status: str):
        """Update OpenAlgo connection status"""
        with self.lock:
            if strategy_name in self.strategies:
                self.strategies[strategy_name]["openalgo_connection"] = status
    
    def get_strategy_health(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Get health data for a specific strategy"""
        with self.lock:
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name].copy()
                
                # Add computed fields
                if strategy["start_time"]:
                    strategy["uptime_seconds"] = (datetime.now() - strategy["start_time"]).total_seconds()
                
                if strategy["last_heartbeat"]:
                    time_since_heartbeat = (datetime.now() - strategy["last_heartbeat"]).total_seconds()
                    strategy["heartbeat_age_seconds"] = time_since_heartbeat
                    strategy["is_responsive"] = time_since_heartbeat < 60  # 1 minute threshold
                else:
                    strategy["is_responsive"] = False
                
                return strategy
        return None
    
    def get_all_strategies_health(self) -> Dict[str, Any]:
        """Get health data for all strategies"""
        with self.lock:
            result = {}
            for strategy_name in self.strategies:
                result[strategy_name] = self.get_strategy_health(strategy_name)
            
            # Add summary
            total_strategies = len(self.strategies)
            running_strategies = sum(1 for s in self.strategies.values() if s["status"] == "running")
            healthy_strategies = sum(1 for s in self.strategies.values() if s["health_score"] > 80)
            
            result["_summary"] = {
                "total_strategies": total_strategies,
                "running_strategies": running_strategies,
                "healthy_strategies": healthy_strategies,
                "timestamp": datetime.now().isoformat()
            }
            
            return result
    
    def cleanup_old_strategies(self, max_age_hours: int = 24):
        """Remove old strategy data"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self.lock:
            to_remove = []
            for strategy_name, data in self.strategies.items():
                if (data["status"] == "stopped" and 
                    data.get("stop_time") and 
                    data["stop_time"] < cutoff_time):
                    to_remove.append(strategy_name)
            
            for strategy_name in to_remove:
                del self.strategies[strategy_name]
                self.logger.info(f"Cleaned up old strategy data: {strategy_name}")
    
    def export_health_data(self, filepath: str):
        """Export health data to JSON file"""
        try:
            data = self.get_all_strategies_health()
            
            # Convert datetime objects to strings for JSON serialization
            def serialize_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj
            
            with open(filepath, 'w') as f:
                json.dump(data, f, default=serialize_datetime, indent=2)
            
            self.logger.info(f"Health data exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to export health data: {e}")

# Global instance
health_monitor = StrategyHealthMonitor()
