# engine\core\data\websocket_client.py

import pandas as pd
import asyncio
from openalgo import api
from datetime import datetime, timedelta

class WebSocketClient:
    def __init__(self, config):
        self.config = config
        self.symbol = config["symbol"]
        self.exchange = config["exchange"]
        self.ltp = 0
        self.ltp_queue = asyncio.Queue()

        self.client = api(api_key=config["api_key"],
                          host=config["host"],
                          ws_url=config["ws_url"])
        asyncio.create_task(self._connect_ws())

    def orderstatus(self, order_id):  
        return self.client.orderstatus(orderid=order_id)     

    async def _connect_ws(self):
        def on_data(data):
            if data.get("type") == "market_data" and data.get("symbol") == self.symbol:
                self.ltp = float(data["data"]["ltp"])
                asyncio.create_task(self.ltp_queue.put(self.ltp))

        self.client.connect()
        self.client.subscribe_ltp([{"exchange": self.exchange, "symbol": self.symbol}], on_data)

    async def get_ohlcv(self):
        now = datetime.now()
        df = self.client.history(symbol=self.symbol,
                                 exchange=self.exchange,
                                 interval=self.config["timeframe"],
                                 start_date=(now - timedelta(hours=2)).strftime("%Y-%m-%d"),
                                 end_date=now.strftime("%Y-%m-%d"))
        return df

    def get_current_ltp(self):
        return self.ltp

    async def get_ltp_event(self):
        return await self.ltp_queue.get()

    def place_order(self, side, quantity):
        return self.client.placeorder(
            strategy="MODULAR_ENGINE",
            symbol=self.symbol,
            exchange=self.exchange,
            action=side,
            price_type=self.config["price_type"],
            product=self.config["product"],
            quantity=quantity
        )

    def funds(self):
        return self.client.funds()
